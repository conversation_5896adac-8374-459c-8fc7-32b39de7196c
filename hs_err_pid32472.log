#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 532676608 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3898), pid=32472, tid=28916
#
# JRE version:  (21.0.7+6) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.7+6-b1038.58, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'http://*************': 

Host: 12th Gen Intel(R) Core(TM) i9-12900H, 20 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Fri Sep 12 16:54:22 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5415) elapsed time: 0.007342 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001edc54077c0):  JavaThread "Unknown thread" [_thread_in_vm, id=28916, stack(0x000000dcc6500000,0x000000dcc6600000) (1024K)]

Stack: [0x000000dcc6500000,0x000000dcc6600000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e7c19]
V  [jvm.dll+0x8c5f53]
V  [jvm.dll+0x8c84ae]
V  [jvm.dll+0x8c8b93]
V  [jvm.dll+0x2899e6]
V  [jvm.dll+0x6e4455]
V  [jvm.dll+0x6d7f1a]
V  [jvm.dll+0x3640db]
V  [jvm.dll+0x36bca6]
V  [jvm.dll+0x3be046]
V  [jvm.dll+0x3be318]
V  [jvm.dll+0x33681c]
V  [jvm.dll+0x33750b]
V  [jvm.dll+0x88d3b9]
V  [jvm.dll+0x3cb218]
V  [jvm.dll+0x876408]
V  [jvm.dll+0x45ff5e]
V  [jvm.dll+0x461c41]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ff8c28fd188, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x000001ede2e806f0 WorkerThread "GC Thread#0"                     [id=9416, stack(0x000000dcc6600000,0x000000dcc6700000) (1024K)]
  0x000001edc547c330 ConcurrentGCThread "G1 Main Marker"            [id=31512, stack(0x000000dcc6700000,0x000000dcc6800000) (1024K)]
  0x000001edc547cc40 WorkerThread "G1 Conc#0"                       [id=19884, stack(0x000000dcc6800000,0x000000dcc6900000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff8c1fe98f7]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ff8c2971bc8] Heap_lock - owner thread: 0x000001edc54077c0

Heap address: 0x0000000605000000, size: 8112 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000605000000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x000001edd9e90000,0x000001eddae70000] _byte_map_base: 0x000001edd6e68000

Marking Bits: (CMBitMap*) 0x000001edc546bf10
 Bits: [0x000001eddae70000, 0x000001ede2d30000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.005 Loaded shared library C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff658fc0000 - 0x00007ff658fca000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.exe
0x00007ff9ad010000 - 0x00007ff9ad227000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff9ab770000 - 0x00007ff9ab834000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff9aa320000 - 0x00007ff9aa6f0000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff9aaad0000 - 0x00007ff9aabe1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff973c80000 - 0x00007ff973c9b000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\VCRUNTIME140.dll
0x00007ff973420000 - 0x00007ff973438000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\jli.dll
0x00007ff9ace10000 - 0x00007ff9acfc1000 	C:\WINDOWS\System32\USER32.dll
0x00007ff9aac70000 - 0x00007ff9aac96000 	C:\WINDOWS\System32\win32u.dll
0x00007ff97d080000 - 0x00007ff97d31c000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe\COMCTL32.dll
0x00007ff9ab910000 - 0x00007ff9ab939000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff9abc40000 - 0x00007ff9abce7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff9aa6f0000 - 0x00007ff9aa813000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff9aa110000 - 0x00007ff9aa1aa000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff9abd00000 - 0x00007ff9abd31000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff973ca0000 - 0x00007ff973cff000 	C:\Program Files\MacType\MacType64.dll
0x00007ff9abb80000 - 0x00007ff9abc31000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff9aaff0000 - 0x00007ff9ab098000 	C:\WINDOWS\System32\sechost.dll
0x00007ff9aaaa0000 - 0x00007ff9aaac8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff9aae60000 - 0x00007ff9aaf78000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff973af0000 - 0x00007ff973c73000 	C:\Program Files\MacType\MacType64.Core.dll
0x00007ff983d20000 - 0x00007ff983d2c000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\vcruntime140_1.dll
0x00007ff8af9a0000 - 0x00007ff8afa2d000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\msvcp140.dll
0x00007ff8c1ca0000 - 0x00007ff8c2a64000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\server\jvm.dll
0x00007ff9ab4f0000 - 0x00007ff9ab561000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff9a9fa0000 - 0x00007ff9a9fed000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff9a1900000 - 0x00007ff9a1934000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff99da00000 - 0x00007ff99da0a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff9a9f80000 - 0x00007ff9a9f93000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff9a9060000 - 0x00007ff9a9078000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff983d10000 - 0x00007ff983d1a000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\jimage.dll
0x00007ff9a7410000 - 0x00007ff9a7643000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff9abd40000 - 0x00007ff9ac0d1000 	C:\WINDOWS\System32\combase.dll
0x00007ff9ab300000 - 0x00007ff9ab3d8000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff98abd0000 - 0x00007ff98ac02000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x000001edc4ae0000 - 0x000001edc4b5b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff973440000 - 0x00007ff973460000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe;C:\Program Files\MacType;C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'http://*************': 
java_class_path (initial): C:/Users/<USER>/AppData/Local/Programs/IntelliJ IDEA Ultimate/plugins/vcs-git/lib/git4idea-rt.jar;C:/Users/<USER>/AppData/Local/Programs/IntelliJ IDEA Ultimate/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 4                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 15                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8506048512                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8506048512                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Users\<USER>\Develop\env\jdk\graalvm-jdk-21.0.3+7.1
CLASSPATH=C:\Users\<USER>\Develop\env\jdk\graalvm-jdk-21.0.3+7.1\lib
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0;C:\windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS;C:\WINDOWS\system32;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\OpenSSH;C:\Users\<USER>\Develop\env\jdk\graalvm-jdk-21.0.3+7.1\bin;C:\Users\<USER>\Develop\env\jdk\graalvm-jdk-21.0.3+7.1\jre\bin;C:\Users\<USER>\Develop\env\maven\apache-maven-3.8.6\bin;C:\Program Files\Bandizip;C:\Program Files\dotnet;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\HP\HP One Agent;C:\Program Files (x86)\NetSarang\Xshell 8;C:\Users\<USER>\Develop\env\gradle\gradle-8.14\bin;C:\Program Files\Git\cmd;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files\vfox;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\PowerShell\7;C:\Users\<USER>\.version-fox\cache\python\current\Scripts;C:\Users\<USER>\.version-fox\cache\python\current;C:\Users\<USER>\.version-fox\cache\nodejs\current;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\go\bin;C:\Program Files\nodejs;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Local\XPipe\cli\bin;C:\Users\<USER>\go\bin;C:\nvm4w\nodejs;C:\Users\<USER>\.deno\bin;C:\Users\<USER>\.jetbrains\helm;C:\Users\<USER>\AppData\Local\Programs\EmEditor
USERNAME=meilon
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 154 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 14584K (0% of 33214232K total physical memory with 4313148K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 0 days 7:49 hours
Hyper-V role detected

CPU: total 20 (initial active 20) (10 cores per cpu, 2 threads per core) family 6 model 154 stepping 3 microcode 0x437, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv, serialize, rdtscp, rdpid, fsrm, f16c, cet_ibt, cet_ss
Processor Information for processor 0
  Max Mhz: 2500, Current Mhz: 1532, Mhz Limit: 2500
Processor Information for processor 1
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 2
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 3
  Max Mhz: 2500, Current Mhz: 1532, Mhz Limit: 2500
Processor Information for processor 4
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 5
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 6
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 7
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 8
  Max Mhz: 2500, Current Mhz: 1532, Mhz Limit: 2500
Processor Information for processor 9
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 10
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 11
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 12
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 13
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 14
  Max Mhz: 2500, Current Mhz: 1527, Mhz Limit: 2500
Processor Information for processor 15
  Max Mhz: 2500, Current Mhz: 1527, Mhz Limit: 2500
Processor Information for processor 16
  Max Mhz: 2500, Current Mhz: 1527, Mhz Limit: 2500
Processor Information for processor 17
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 18
  Max Mhz: 2500, Current Mhz: 1527, Mhz Limit: 2500
Processor Information for processor 19
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500

Memory: 4k page, system-wide physical 32435M (4212M free)
TotalPageFile size 32435M (AvailPageFile size 471M)
current process WorkingSet (physical memory assigned to process): 14M, peak: 14M
current process commit charge ("private bytes"): 76M, peak: 584M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-b1038.58) for windows-amd64 JRE (21.0.7+6-b1038.58), built on 2025-07-07 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
