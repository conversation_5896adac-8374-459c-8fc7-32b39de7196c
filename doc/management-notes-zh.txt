OpenVPN 管理接口说明
----------------------------------

OpenVPN 管理接口允许通过 TCP 或 unix 域套接字从外部程序对 OpenVPN 进行管理控制。

该接口专门为希望以编程方式或远程控制 OpenVPN 守护进程的开发人员设计，可在 OpenVPN 作为客户端或服务器运行时使用。

管理接口使用客户端/服务器 TCP 连接或 unix 域套接字实现，OpenVPN 将在提供的 IP 地址和端口上监听传入的管理接口客户端连接。

管理协议目前是明文的，没有显式的安全层。因此，建议管理接口监听 unix 域套接字、localhost (127.0.0.1) 或本地 VPN 地址。可以通过 VPN 本身远程连接到管理接口，但在此模式下某些功能将受到限制，例如提供私钥密码的能力。

在 OpenVPN 配置文件中使用以下指令启用管理接口：

--management

有关此指令和相关指令的文档，请参阅手册页。

一旦 OpenVPN 启动并启用了管理层，您可以 telnet 到管理端口（确保使用理解"原始"模式的 telnet 客户端）。

连接到管理端口后，您可以使用 "help" 命令列出所有命令。

命令 -- bytecount
--------------------

bytecount 命令用于请求 OpenVPN 带宽使用的实时通知。

命令语法：

  bytecount n (其中 n > 0) -- 设置每 n 秒自动通知带宽使用情况
  bytecount 0 -- 关闭 bytecount 通知

如果 OpenVPN 作为客户端运行，bytecount 通知将如下所示：

  >BYTECOUNT:{BYTES_IN},{BYTES_OUT}

BYTES_IN 是从服务器接收的字节数，BYTES_OUT 是发送到服务器的字节数。

如果 OpenVPN 作为服务器运行，bytecount 通知将如下所示：

  >BYTECOUNT_CLI:{CID},{BYTES_IN},{BYTES_OUT}
 
CID 是客户端 ID，BYTES_IN 是从客户端接收的字节数，BYTES_OUT 是发送到客户端的字节数。

请注意，当在服务器上使用 bytecount 命令时，每个连接的客户端都会每 n 秒报告其带宽数据。

当客户端断开连接时，最终的带宽数据将放在 'bytes_received' 和 'bytes_sent' 环境变量中，包含在 >CLIENT:DISCONNECT 通知中。

命令 -- echo
---------------

echo 功能用于允许将 GUI 特定参数嵌入到 OpenVPN 配置文件中或从服务器推送到 OpenVPN 客户端。

命令示例：

  echo on      -- 开启 echo 消息的实时通知
  echo all     -- 打印当前 echo 历史列表
  echo off     -- 关闭 echo 消息的实时通知
  echo on all  -- 原子性地启用实时通知，并显示历史缓冲区中的任何消息

例如，假设您正在开发一个 OpenVPN GUI，并且您希望给 OpenVPN 服务器提供要求 GUI 忘记任何已保存密码的能力。

在 OpenVPN 服务器配置文件中，添加：

  push "echo forget-passwords"

当 OpenVPN 客户端从服务器接收其拉取的指令列表时，"echo forget-passwords" 指令将在列表中，它将导致管理接口将 "forget-passwords" 字符串保存在其 echo 参数列表中。

管理接口客户端可以使用 "echo all" 输出完整的回显参数列表，"echo on" 开启通过 ">ECHO:" 前缀的回显参数实时通知，或 "echo off" 关闭实时通知。

当 GUI 连接到 OpenVPN 管理套接字时，它可以发出 "echo all" 命令，这将产生如下输出：

  1101519562,forget-passwords
  END

本质上，echo 命令允许我们将参数从 OpenVPN 服务器传递到 OpenVPN 客户端，然后传递到管理接口客户端（如 GUI）。大整数是接收 echo 参数时的 unix 日期/时间。

如果管理接口客户端发出了命令 "echo on"，它将启用 echo 参数的实时通知。在这种情况下，我们的 "forget-passwords" 消息将如下输出：

  >ECHO:1101519562,forget-passwords

与 log 命令一样，echo 命令可以原子性地显示历史记录，同时激活实时更新：

  echo on all

echo 缓冲区的大小目前硬编码为 100 条消息。


一般来说，OpenVPN 核心根本不理解 echo 消息（因此协作的 GUI 和服务器可以使用此机制进行任意信息传输）。

话虽如此，社区维护的 OpenVPN Windows GUI 和 MacOS 的 Tunnelblick 之间已经商定了一些 echo 命令，这些命令的文档可以在 doc/gui-notes.txt 中找到。


命令 -- exit, quit
---------------------

关闭管理会话，并恢复在管理端口上监听来自其他客户端的连接。目前，OpenVPN 守护进程最多只能同时支持一个管理接口客户端。

命令 -- help
---------------

打印命令摘要。

命令 -- hold
---------------

hold 命令可用于操作保持标志，或从保持状态释放 OpenVPN。

如果在初始启动或重启时设置了保持标志，OpenVPN 将在初始化隧道之前休眠，直到管理接口接收到 "hold release" 命令。

OpenVPN 的 --management-hold 指令可用于在设置保持标志的情况下启动 OpenVPN。

保持标志设置是持久的，不会被重启重置。

OpenVPN 将通过向管理接口客户端发送实时通知来指示它处于保持状态，参数指示 OpenVPN 在没有 UI 的情况下会等待多长时间（受连接重试指数退避影响）。如果 UI 想要类似的行为，需要等待释放保持：

  >HOLD:Waiting for hold release:10

命令示例：

  hold         -- 显示当前保持标志，0=关闭，1=开启。
  hold on      -- 开启保持标志，以便将来的重启会保持。
  hold off     -- 关闭保持标志，以便将来的重启不会保持。
  hold release -- 离开保持状态并启动 OpenVPN，但不改变当前保持标志设置。

命令 -- kill
---------------

在服务器模式下，杀死特定的客户端实例。

命令示例：

  kill Test-Client -- 杀死具有通用名称 "Test-Client" 的客户端实例。
  kill tcp:*******:4000 -- 杀死具有源地址、端口和协议为 tcp:*******:4000 的客户端实例

  请注意，按地址杀死对 IPv6 连接的客户端尚不起作用，因此请依赖按 CN 或 CID 杀死。

使用 "status" 命令查看哪些客户端已连接。

命令 -- log
--------------

显示 OpenVPN 日志文件。管理接口只缓存日志文件的最近 n 行，其中 n 由 OpenVPN --management-log-cache 指令控制。

命令示例：

  log on     -- 启用日志消息的实时输出。
  log all    -- 显示当前缓存的日志文件历史记录。
  log on all -- 原子性地显示所有当前缓存的日志文件历史记录，然后启用新日志文件消息的实时通知。
  log off    -- 关闭日志消息的实时通知。
  log 20     -- 显示日志文件历史记录的最近 20 行。

实时通知格式：

实时日志消息以 ">LOG:" 前缀开头，后跟以下逗号分隔的字段：

  (a) unix 整数日期/时间，
  (b) 单个字符串中的零个或多个消息标志：
      I -- 信息性
      F -- 致命错误
      N -- 非致命错误
      W -- 警告
      D -- 调试，以及
  (c) 消息文本。

命令 -- mute
---------------

更改 OpenVPN --mute 参数。mute 参数用于静音同一消息类别的重复消息。

命令示例：

  mute 40 -- 将 mute 参数更改为 40
  mute    -- 显示当前 mute 设置

命令 -- net
--------------

（仅限 Windows）产生等效于 OpenVPN --show-net 指令的输出。输出包括 OpenVPN 对系统网络适配器列表和路由表的视图，基于 Windows IP 助手 API 返回的信息。

命令 -- pid
--------------

显示当前 OpenVPN 进程的进程 ID。

命令 -- password 和 username
--------------------------------

  password 命令用于向 OpenVPN 传递密码。

  如果 OpenVPN 使用 --management-query-passwords 指令运行，它将向管理接口查询 RSA 私钥密码和 --auth-user-pass 用户名/密码。

  当 OpenVPN 需要来自管理接口的密码时，它将产生实时 ">PASSWORD:" 消息。

  示例 1：

    >PASSWORD:Need 'Private Key' password

  OpenVPN 指示它需要 "Private Key" 类型的密码。

  管理接口客户端应如下响应：

    password "Private Key" foo

  示例 2：

    >PASSWORD:Need 'Auth' username/password

  OpenVPN 需要 --auth-user-pass 用户名和密码。管理接口客户端应响应：

    username "Auth" foo
    password "Auth" bar

  用户名/密码本身可以用引号括起来，特殊字符如双引号或反斜杠必须转义，例如，

    password "Private Key" "foo\"bar"

  转义规则与配置文件相同。有关更多信息，请参阅下面的"命令解析"部分。

  PASSWORD 实时消息类型也可用于指示密码或其他类型的身份验证失败：

  示例 3：私钥密码不正确，OpenVPN 正在退出：

    >PASSWORD:Verification Failed: 'Private Key'

  示例 4：--auth-user-pass 用户名/密码失败，如果 '--auth-retry none'（这是默认值）生效，OpenVPN 将以致命错误退出：

    >PASSWORD:Verification Failed: 'Auth'

  示例 5：--auth-user-pass 用户名/密码失败，服务器使用客户端拒绝服务器端管理接口命令提供了自定义客户端原因文本字符串。

    >PASSWORD:Verification Failed: 'custom server-generated string'

  示例 6：如果服务器向客户端推送 --auth-token，OpenVPN 将产生实时 PASSWORD 消息：

    >PASSWORD:Auth-Token:foobar

  示例 7：静态挑战/响应：

    >PASSWORD:Need 'Auth' username/password SC:1,Please enter token PIN

  OpenVPN 需要 --auth-user-pass 用户名和密码以及对挑战的响应。应获取用户对 "Please enter token PIN" 的响应，并将其包含在管理接口客户端的响应中，与用户名和密码一起，按照下面挑战/响应协议部分中描述的格式。

  示例 8：动态挑战/响应：

    >PASSWORD:Verification Failed: ['CRV1:R,E:Om01u7Fh4LrGBS7uh0SWmzwabUiGiW6l:Y3Ix:Please enter token PIN']

  之前的 --auth-user-pass 用户名/密码失败或不完整，服务器提供了自定义客户端原因文本字符串，指示下次看到 "Need 'Auth' username/password" 消息时应进行动态挑战/响应。

  当下次看到没有静态挑战的 "Need 'Auth' username/password" 时，应获取用户对 "Please enter token PIN" 的响应，并将其包含在管理接口客户端的响应中，与用户名和密码一起，按照下面挑战/响应协议部分中描述的格式

有关示例 7 和 8 的更多详细信息，包括管理接口客户端应如何响应，请参阅下面的"挑战/响应协议"部分。

命令 -- forget-passwords
---------------------------

forget-passwords 命令将导致守护进程忘记会话期间输入的密码。

命令示例：

  forget-passwords -- 忘记到目前为止输入的密码。

命令 -- signal
-----------------

signal 命令将向 OpenVPN 守护进程发送信号。信号可以是 SIGHUP、SIGTERM、SIGUSR1 或 SIGUSR2 之一。

命令示例：

  signal SIGUSR1 -- 向守护进程发送 SIGUSR1 信号

命令 -- state
----------------

显示当前 OpenVPN 状态、显示状态历史记录或启用状态更改的实时通知。

这些是 OpenVPN 状态：

CONNECTING    -- OpenVPN 的初始状态。
WAIT          -- （仅客户端）等待来自服务器的初始响应。
AUTH          -- （仅客户端）与服务器进行身份验证。
GET_CONFIG    -- （仅客户端）从服务器下载配置选项。
ASSIGN_IP     -- 为虚拟网络接口分配 IP 地址。
ADD_ROUTES    -- 向系统添加路由。
CONNECTED     -- 初始化序列完成。
RECONNECTING  -- 发生了重启。
EXITING       -- 正在进行优雅退出。
RESOLVE       -- （仅客户端）DNS 查找
TCP_CONNECT   -- （仅客户端）连接到 TCP 服务器
AUTH_PENDING  -- （仅客户端）身份验证待定

命令示例：

  state        -- 打印当前 OpenVPN 状态。
  state on     -- 启用状态更改的实时通知。
  state off    -- 禁用状态更改的实时通知。
  state all    -- 打印当前状态历史记录。
  state 3      -- 打印最近的 3 次状态转换。
  state on all -- 原子性地显示状态历史记录，同时启用未来状态转换的实时状态通知。

输出格式包含最多 9 个逗号分隔的参数：
  (a) 整数 unix 日期/时间，
  (b) 状态名称，
  (c) 可选的描述性字符串（主要在 RECONNECTING 和 EXITING 上使用，显示断开连接的原因），
  (d) 可选的 TUN/TAP 本地 IPv4 地址
  (e) 可选的远程服务器地址，
  (f) 可选的远程服务器端口，
  (g) 可选的本地地址，
  (h) 可选的本地端口，以及
  (i) 可选的 TUN/TAP 本地 IPv6 地址。

字段 (e)-(h) 显示 CONNECTED 状态，
(d) 和 (i) 显示 ASSIGN_IP 和 CONNECTED 状态。

(e) 从 OpenVPN 2.1 开始可用
(f)-(i) 从 OpenVPN 2.4 开始可用

对于 AUTH_PENDING，如果存在 (c)，它将读取为 "timeout number"，其中 number 是身份验证超时前的秒数。它打印为无符号整数 (%u)。

实时状态通知将在前面加上 ">STATE:" 前缀。

命令 -- status
-----------------

显示当前守护进程状态信息，格式与 OpenVPN --status 指令产生的格式相同。

命令示例：

status   -- 使用默认状态格式版本显示状态信息。

status 3 -- 使用 --status-version 3 的格式显示状态信息。

命令 -- username
-------------------

请参阅上面的 "password" 部分。

命令 -- verb
---------------

更改 OpenVPN --verb 参数。verb 参数控制输出详细程度，可能范围从 0（无输出）到 15（最大输出）。有关详细程度级别的其他信息，请参阅 OpenVPN 手册页。

命令示例：

  verb 4  -- 将 verb 参数更改为 4
  verb    -- 显示当前 verb 设置

命令 -- version
------------------

设置客户端支持的管理接口版本（整数）或显示当前 OpenVPN 和管理接口版本。

命令示例：
  version 2  -- 将客户端的管理版本更改为 2（默认 = 1）
  version    -- 显示 OpenVPN 及其管理接口的版本

命令 -- auth-retry
---------------------

设置 --auth-retry 设置以控制 OpenVPN 如何响应用户名/密码身份验证错误。有关更多信息，请参阅手册页。

命令示例：

  auth-retry interact -- 输入错误的用户名/密码时不要退出。查询新输入并重试。

命令 -- needok  (OpenVPN 2.1 或更高版本)
------------------------------------------

确认 ">NEED-OK" 实时通知，通常由 OpenVPN 用于在等待特定用户操作时阻塞。

示例：

  OpenVPN 需要用户插入加密令牌，因此它发送实时通知：

    >NEED-OK:Need 'token-insertion-request' confirmation MSG:Please insert your cryptographic token

  管理接口客户端，如果它是 GUI，可以向用户闪烁包含 "MSG:" 标记后文本的对话框。当用户确认对话框时，管理接口客户端应发出以下任一命令：

     needok token-insertion-request ok
  或
     needok token-insertion-request cancel

命令 -- needstr  (OpenVPN 2.1 或更高版本)
-------------------------------------------

确认 ">NEED-STR" 实时通知，通常由 OpenVPN 用于在等待特定用户输入时阻塞。

示例：

  OpenVPN 需要用户指定一些输入，因此它发送实时通知：

    >NEED-STR:Need 'name' input MSG:Please specify your name

  管理接口客户端，如果它是 GUI，可以向用户闪烁包含 "MSG:" 标记后文本的对话框。当用户确认对话框时，管理接口客户端应发出此命令：

     needstr name "John"

命令 -- pkcs11-id-count  (OpenVPN 2.1 或更高版本)
---------------------------------------------------

检索可用证书的数量。

示例：

     pkcs11-id-count
     >PKCS11ID-COUNT:5

命令 -- pkcs11-id-get  (OpenVPN 2.1 或更高版本)
-------------------------------------------------

按索引检索证书，ID 字符串应作为 PKCS#11 身份提供，blob 是 base 64 编码的证书。

示例：

     pkcs11-id-get 1
     PKCS11ID-ENTRY:'1', ID:'<snip>', BLOB:'<snip>'

命令 -- client-auth  (OpenVPN 2.1 或更高版本)
-----------------------------------------------

授权 ">CLIENT:CONNECT" 或 ">CLIENT:REAUTH" 请求，并在后续文本块中指定 "client-connect" 配置指令。

OpenVPN 服务器应该使用 --management-client-auth 指令启动，以便它要求管理接口批准客户端连接。


  client-auth {CID} {KID}
  line_1
  line_2
  ...
  line_n
  END

CID,KID -- 客户端 ID 和密钥 ID。有关更多信息，请参阅 ">CLIENT:" 通知的文档。

line_1 到 line_n -- client-connect 配置文本块，如 --client-connect 脚本返回的那样。文本块可能为空，"END" 紧跟在 "client-auth" 行之后（使用空文本块等效于使用 client-auth-nt 命令）。

client-connect 配置文本块包含将应用于表示新连接客户端的客户端实例对象的 OpenVPN 指令。

命令 -- client-auth-nt  (OpenVPN 2.1 或更高版本)
--------------------------------------------------

授权 ">CLIENT:CONNECT" 或 ">CLIENT:REAUTH" 请求，而不指定 client-connect 配置文本。

OpenVPN 服务器应该使用 --management-client-auth 指令启动，以便它要求管理接口批准客户端连接。

  client-auth-nt {CID} {KID}

CID,KID -- 客户端 ID 和密钥 ID。有关更多信息，请参阅 ">CLIENT:" 通知的文档。

命令 -- client-pending-auth  (OpenVPN 2.5 或更高版本)
----------------------------------------------------

指示 OpenVPN 服务器向客户端发送 AUTH_PENDING 和 INFO_PRE 消息，以表示待定身份验证。待定身份验证意味着连接需要额外的身份验证，如一次性密码或通过网络进行单点登录。

    client-pending-auth {CID} {KID} {EXTRA} {TIMEOUT}

服务器将向客户端发送 AUTH_PENDING 和 INFO_PRE,{EXTRA}。如果客户端支持接受 AUTH_PENDING 的关键字（通过 IV_PROTO 宣布），TIMEOUT 参数也将向客户端宣布，以允许它修改自己的超时。客户端应该通知用户身份验证待定并显示额外信息，如果适用，还应显示用户完成身份验证的剩余时间。

接收 AUTH_PENDING 消息将使客户端将其超时更改为服务器建议的超时，即使超时更短。如果客户端在 hand-window 时间内没有从服务器接收到数据包，连接仍会超时，无论超时如何。这确保在网络问题的情况下连接仍然相对快速地超时。客户端将持续向服务器发送 PULL_REQUEST 消息，直到达到超时。此消息还会触发来自服务器的 ACK 消息，该消息重置基于 hand-window 的超时。

客户端和服务器都将最大超时限制为 --tls-reneg 最小时间和 --hand-window 时间的一半的较小值（默认为 60 秒）。

有关 {EXTRA} 的格式，请参见下文。对于 OpenVPN 服务器，这是一个无状态操作，需要跟随 client-deny/client-auth[-nt] 命令（这是带外身份验证的结果）。

请注意，{KID} 参数已在管理版本 5 中添加，用于指定身份验证所属的待定客户端密钥。这确保待定身份验证消息严格绑定到身份验证会话。

在向客户端发出 client-pending-auth 而不是 client-auth/client-deny 之前，服务器应检查 IV_SSO 环境变量以了解是否支持该方法。目前，定义的方法有用于使用文本进行挑战/响应的 crtext（例如，TOTP）、openurl（已弃用）和用于在客户端中打开 URL 以继续身份验证的 webauth。支持 webauth 和 crtext 的客户端将设置

    setenv IV_SSO webauth,crtext

变量名 IV_SSO 是历史性的，因为 AUTH_PENDING 最初用于表示单点登录支持。为了与现有实现保持兼容性，保留名称 IV_SSO 而不是更好的名称。

客户端的管理接口通过以下方式接收待定身份验证的通知

>STATE:datetime,AUTH_PENDING,[timeout number],,,,,

如果存在 {EXTRA}，客户端将使用 INFOMSG 通知得到通知

>INFOMSG:{EXTRA}

其中 {EXTRA} 的格式与从服务器接收的格式相同。
目前为 {EXTRA} 定义的格式详述如下。

webauth 和 openurl
===================
对于基于网络的额外身份验证（如 SSO/SAML），{EXTRA} 应该是

    OPEN_URL:url

或

    WEB_AUTH:flags:url

OPEN_URL 方法已弃用，因为它不允许发送标志，而标志被证明是向客户端发出某些行为信号所必需的。

客户端应要求用户打开 URL 以继续。

控制消息中的空间有限，因此此 url 应保持简短以避免问题。如果需要更长的 url，应发送重定向到更长 URL 的 URL。总长度限制为 1024 字节，包括 INFO_PRE:WEB_AUTH:flags。

flags 是由逗号分隔的标志列表。目前定义的标志有：

- proxy     （见下一段）
- hidden    以隐藏模式启动 webview（参见 openvpn3 webauth 文档）
- external  不使用内部 webview，而使用外部浏览器。某些身份验证提供程序拒绝在内部 webview 中工作。


有关客户端应如何处理 URL 的完整文档可在 openvpn3 存储库中找到：

https://github.com/OpenVPN/openvpn3/blob/master/doc/webauth.md

带代理的 webauth
==================
这是 webauth 的一个变体，允许通过 HTTP 代理打开 url。它可用于避免 OpenVPN 连接的 persist-tun 可能导致 Web 服务器无法访问的问题。客户端应在其 IV_SSO 中宣布代理并解析 WEB_AUTH 消息中的代理标志。在这种情况下，{EXTRA} 的格式是

    WEB_AUTH:proxy=<proxy>;<proxy_port>;<proxy_user_base64>;<proxy_password_base64>,flags:url

代理应该是字面 IPv4 地址或用 [] 括起来的 IPv6 地址，以避免解析中的歧义。首选字面 IP 地址，因为当客户端需要打开 url 时 DNS 可能不可用。IP 地址通常是客户端用于连接到 VPN 服务器的地址。对于双宿主 VPN 服务器，服务器应响应客户端连接到的相同地址。

此地址通常也被主机路由排除在通过 VPN 重定向之外。如果平台（如 Android）使用另一种方式保护 VPN 连接免受路由循环，客户端还需要以相同方式排除到代理的连接。

如果使用另一个 IP，则 VPN 配置应包含路由语句以排除该地址通过 VPN 路由。

crtext
=======
{EXTRA} 的格式类似于本文档挑战/响应协议部分中描述的已使用的两步身份验证。由于大多数字段不是必需的或可以推断，只使用 <flags> 和 <challenge_text> 字段：

    CR_TEXT:<flags>:<challenge_text>

<flags>：一系列可选的、逗号分隔的标志：
 E：当用户键入响应时回显响应。
 R：需要响应。

<challenge_text>：要向用户显示的挑战文本。

客户端应使用 cr-response 命令返回对 crtext 挑战的响应。

命令 -- client-deny  (OpenVPN 2.1 或更高版本)
-----------------------------------------------

拒绝 ">CLIENT:CONNECT" 或 ">CLIENT:REAUTH" 请求。

  client-deny {CID} {KID} "reason-text" ["client-reason-text"]

CID,KID -- 客户端 ID 和密钥 ID。有关更多信息，请参阅 ">CLIENT:" 通知的文档。

reason-text：解释为什么身份验证请求被拒绝的人类可读消息。此消息将输出到 OpenVPN 日志文件或 syslog。

client-reason-text：将作为 AUTH_FAILED 消息的一部分发送给客户端的消息。

请注意，client-deny 拒绝特定的密钥 ID（与 TLS 重新协商相关）。响应初始 TLS 密钥协商（由 ">CLIENT:CONNECT" 通知）发出的 client-deny 命令将在向客户端返回 "AUTH-FAILED" 后终止客户端会话。另一方面，响应 TLS 重新协商（">CLIENT:REAUTH"）发出的 client-deny 命令将使重新协商的密钥无效，但是与当前活动密钥关联的 TLS 会话将继续存在最多 --tran-window 秒直到过期。

要立即杀死客户端会话，请使用 "client-kill"。

命令 -- client-kill  (OpenVPN 2.1 或更高版本)
-----------------------------------------------

通过 CID 立即杀死客户端实例。

  client-kill {CID}

CID -- 客户端 ID。有关更多信息，请参阅 ">CLIENT:" 通知的文档。

命令 -- remote-entry-count (OpenVPN 2.6+ 管理版本 > 3)
-------------------------------------------------------------------

检索可用远程主机/端口条目的数量

示例：

  管理接口客户端发送：

    remote-entry-count

  OpenVPN 守护进程响应

  5
  END

命令 -- remote-entry-get (OpenVPN 2.6+ 管理版本 > 3)
------------------------------------------------------------------

  remote-entry-get <start> [<end>]

检索索引 <start> 或从 <start> 到 <end>-1 的索引的远程条目（主机、端口、协议和状态）。或者 <start> = "all" 检索所有远程条目。索引从 0 开始。如果由于协议或代理限制而禁用条目（即，ce->flag & CE_DISABLED == 1），状态返回为 "disabled"，否则读取为 "enabled"，不带引号。

示例 1：

  管理接口客户端发送：

    remote-entry-get 1

  OpenVPN 守护进程响应

    1,vpn.example.com,1194,udp,enabled
    END

示例 2：

  管理接口客户端发送：

    remote-entry-get 1 3

  OpenVPN 守护进程响应

    1,vpn.example.com,1194,udp,enabled
    2,vpn.example.net,443,tcp-client,disabled
    END

示例 3：
  管理接口客户端发送：

    remote-entry-get all

  具有 3 个连接条目的 OpenVPN 守护进程响应

    0,vpn.example.com,1194,udp,enabled
    1,vpn.example.com,443,tcp-client,enabled
    2,vpn.example.net,443,udp,enabled
    END

命令 -- remote  (OpenVPN AS 2.1.5/OpenVPN 2.3 或更高版本)
--------------------------------------------

响应 >REMOTE 通知提供远程主机/端口（仅客户端）。需要使用 --management-query-remote 指令。

  remote ACTION [HOST PORT]

"remote" 命令只应响应 >REMOTE 通知给出。例如，以下 >REMOTE 通知表示客户端配置文件通常会连接到 vpn.example.com 端口 1194 (UDP)：

  >REMOTE:vpn.example.com,1194,udp

现在，假设我们想要覆盖主机和端口，改为连接到 vpn.otherexample.com 端口 1234。在接收到上述通知后，使用此命令：

  remote MOD vpn.otherexample.com 1234

要接受客户端通常会连接到的相同主机和端口，请使用此命令：

  remote ACCEPT

要跳过当前连接条目并前进到下一个，请使用此命令：

  remote SKIP

从 OpenVPN 版本 2.6（管理版本 > 3）开始，使用以下命令跳过多个远程：

  remote SKIP n

其中 n > 0 是要跳过的远程数量。

命令 -- proxy  (OpenVPN 2.3 或更高版本)
--------------------------------------------

响应 >PROXY 通知提供代理服务器主机/端口和标志（仅客户端）。需要使用 --management-query-proxy 指令。

  proxy TYPE HOST PORT ["nct"]

"proxy" 命令只能响应 >PROXY 通知给出。如果您只想允许与代理服务器进行非明文身份验证，请使用 "nct" 标志。以下 >PROXY 通知表示客户端配置文件通常会使用 TCP 连接到第一个配置的 --remote，vpn.example.com：

  >PROXY:1,TCP,vpn.example.com

现在，假设我们想要使用代理服务器 proxy.intranet 端口 8080 连接到远程主机，如果需要，只进行安全身份验证。在接收到上述通知后，使用此命令：

  proxy HTTP proxy.intranet 8080 nct

您也可以使用 SOCKS 关键字传递 SOCKS 服务器地址，如：

  proxy SOCKS fe00::1 1080

要接受直接连接到主机和端口，请使用此命令：

  proxy NONE

命令 -- cr-response (OpenVPN 2.5 或更高版本)
-------------------------------------------------
提供对通过 INFOMSG,CR_TEXT 进行挑战/响应查询的响应支持（仅客户端）。响应应进行 base64 编码：

  cr-response SGFsbG8gV2VsdCE=

此命令旨在在客户端接收到 CR_TEXT 挑战后使用（参见 client-pending-auth 部分）。cr-response 的参数是对挑战的 base64 编码答案，取决于挑战本身。对于 TOTP 挑战，这将是编码为 base64 的数字；对于像"今天是星期几？"这样的挑战，它将是编码为 base64 的字符串。

命令 -- pk-sig (OpenVPN 2.5 或更高版本，管理版本 > 1)
命令 -- rsa-sig (OpenVPN 2.3 或更高版本，管理版本 <= 1)
-----------------------------------------------------------------
提供对私钥外部存储的支持。需要 --management-external-key 选项。此选项可以在客户端模式下代替 "key" 使用，并允许客户端在无需加载实际私钥的情况下运行。当 SSL 协议需要执行签名操作时，要签名的数据将通过以下通知发送到管理接口：

>PK_SIGN:[BASE64_DATA],[ALG] (如果客户端宣布支持管理版本 > 2)
>PK_SIGN:[BASE64_DATA] (如果客户端宣布支持管理版本 > 1)
>RSA_SIGN:[BASE64_DATA] (只有较旧的客户端会这样提示)

管理接口客户端应使用私钥创建（解码的）BASE64_DATA 的适当签名，并按如下方式返回 SSL 签名：

pk-sig (或 rsa-sig)
[BASE64_SIG_LINE]
.
.
.
END

使用 OpenSSL 的 RSA_private_encrypt 对 RSA 或 ECDSA_sign() 对 EC 的 Base 64 编码输出，或使用 mbed TLS 的 mbedtls_pk_sign() 将提供正确的签名。
rsa-sig 接口期望 RSA 密钥的 PKCS1 填充签名 (RSA_PKCS1_PADDING)。EC 签名始终未填充。

此功能旨在允许通过管理接口将任意加密服务提供程序与 OpenVPN 一起使用。

新的和更新的客户端应使用 version 命令宣布版本 > 1 并处理 '>PK_SIGN' 提示并使用 'pk-sig' 响应。

只有当管理客户端版本 > 2 时，签名算法才在 PK_SIGN 请求中指示。特别是，为了支持使用 OpenSSL 1.1.1 的 TLS1.3 和 TLS1.2，需要未填充签名支持，只有当客户端版本 > 2 时才能在签名请求中指示这一点"

当前定义的填充算法有：

 - RSA_PKCS1_PADDING            -  PKCS1 填充和 RSA 签名
 - RSA_NO_PADDING               -  签名不得添加填充
 - ECDSA                        -  EC 签名。
 - RSA_PKCS1_PSS_PADDING,params -  带 PSS 填充的 RSA 签名

   PSS 的参数指定为 'hashalg=name,saltlen=[max|digest]'。

   hashalg 名称是短的通用名称，如 SHA256、SHA224 等。
   PSS saltlen="digest" 意味着使用与要签名的哈希相同的大小，而 "max" 表示最大可能的 saltlen，即 '(nbits-1)/8 - hlen - 2'。这里 'nbits' 是密钥模数中的位数，'hlen' 是哈希的八位字节大小。
   （参见：RFC 8017 第 8.1.1 和 9.1.1 节）

   在 PKCS1_PADDING 的情况下，当哈希算法不是传统的 MD5-SHA1 时，哈希在呈现给管理接口之前用 DigestInfo 头编码。这与 Cryptoki 中的 CKM_RSA_PKCS 以及 OpenSSL 中的 RSA_private_encrypt() 期望的相同。

命令 -- certificate (OpenVPN 2.4 或更高版本)
----------------------------------------------
提供对证书外部存储的支持。需要 --management-external-cert 选项。此选项可以在客户端模式下代替 "cert" 使用。在 SSL 协议初始化时，将向管理接口发送带有提示的通知，如下所示：

>NEED-CERTIFICATE:macosx-keychain:subject:o=OpenVPN-TEST

管理接口客户端应使用提示获取特定的 SSL 证书，然后按如下方式返回 base 64 编码的证书：

certificate
[BASE64_CERT_LINE]
.
.
.
END

此功能旨在允许将存储在文件系统外部的证书（例如在 Mac OS X Keychain 中）通过管理接口与 OpenVPN 一起使用。

输出格式
-------------

(1) 命令成功/失败由 "SUCCESS: [text]" 或 "ERROR: [text]" 指示。

(2) 对于打印多行输出的命令，最后一行将是 "END"。

(3) 实时消息将采用 ">[source]:[text]" 的形式，其中 source 是 "CLIENT"、"ECHO"、"FATAL"、"HOLD"、"INFO"、"LOG"、"NEED-OK"、"PASSWORD" 或 "STATE"。

实时消息格式
------------------------

OpenVPN 管理接口产生两种输出：(a) 来自命令的输出，或 (b) 可以在任何时候生成的异步、实时输出。

实时消息在第一列以 '>' 字符开头，紧跟一个类型关键字，指示实时消息的类型。目前定义了以下类型：

BYTECOUNT -- 实时带宽使用通知，当 OpenVPN 作为客户端运行时由 "bytecount" 命令启用。

BYTECOUNT_CLI -- 每客户端实时带宽使用通知，当 OpenVPN 作为服务器运行时由 "bytecount" 命令启用。

CLIENT   -- OpenVPN 服务器上客户端连接和断开连接的通知。当 OpenVPN 使用 --management-client-auth 选项启动时启用。CLIENT 通知可能是多行的。有关详细信息，请参阅下面的"CLIENT 通知"部分。

ECHO     -- 由 "echo" 命令控制的回显消息。

FATAL    -- 在 OpenVPN 退出之前输出到日志文件的致命错误。

HOLD     -- 用于指示 OpenVPN 处于保持状态，在接收到 "hold release" 命令之前不会启动。

INFO     -- 信息性消息，如欢迎消息。

LOG      -- 由 "log" 命令控制的日志消息输出。

NEED-OK  -- OpenVPN 需要最终用户做某事，如插入加密令牌。"needok" 命令可用于告诉 OpenVPN 继续。

NEED-STR -- OpenVPN 需要来自最终用户的信息，如要使用的证书。"needstr" 命令可用于告诉 OpenVPN 继续。

PASSWORD -- 用于告诉管理接口客户端 OpenVPN 需要密码，也用于指示密码验证失败。

STATE    -- 显示当前 OpenVPN 状态，由 "state" 命令控制。

INFOMSG  -- 来自服务器的身份验证相关信息，如 CR_TEXT 或 OPEN_URL。请参阅 client-pending-auth 下的描述

CLIENT 通知
-----------------------

">CLIENT:" 通知由 --management-client-auth OpenVPN 配置指令启用，该指令使管理接口客户端负责在验证 OpenVPN 客户端的客户端证书后对其进行身份验证。CLIENT 通知可能是多行的，给定 CLIENT 通知、其关联的环境变量和终止的 ">CLIENT:ENV,END" 行的顺序性保证是原子的。

CLIENT 通知类型：

(1) 通知新客户端连接（"CONNECT"）或现有客户端 TLS 会话重新协商（"REAUTH"）。有关客户端的信息由环境变量列表提供，这些变量在 OpenVPN 手册页中有文档记录。传递的环境变量等效于传递给 --auth-user-pass-verify 脚本的变量。

    >CLIENT:CONNECT|REAUTH,{CID},{KID}
    >CLIENT:ENV,name1=val1
    >CLIENT:ENV,name2=val2
    >CLIENT:ENV,...
    >CLIENT:ENV,END

(2) 通知成功的客户端身份验证和会话启动。在 CONNECT 之后调用。

    >CLIENT:ESTABLISHED,{CID}
    >CLIENT:ENV,name1=val1
    >CLIENT:ENV,name2=val2
    >CLIENT:ENV,...
    >CLIENT:ENV,END

(3) 通知现有客户端断开连接。传递的环境变量等效于传递给 --client-disconnect 脚本的变量。

    >CLIENT:DISCONNECT,{CID}
    >CLIENT:ENV,name1=val1
    >CLIENT:ENV,name2=val2
    >CLIENT:ENV,...
    >CLIENT:ENV,END

(4) 通知特定虚拟地址或子网现在与特定客户端关联。

    >CLIENT:ADDRESS,{CID},{ADDR},{PRI}

(5) 基于文本的挑战/响应

   >CLIENT:CR_RESPONSE,{CID},{KID},{response_base64}
   >CLIENT:ENV,name1=val1
   >CLIENT:ENV,name2=val2
   >CLIENT:ENV,...
   >CLIENT:ENV,END

   在客户端使用 cr-response 命令将在服务器端触发此消息。

   CR_RESPONSE 通知与传统挑战/响应中的 CRV1 响应具有相同的目的。有关更多详细信息，请参阅下面的该部分。由于这使用与原始 client-pending-auth 挑战中相同的 cid，我们不在此通知中包含用户名和不透明会话数据。字符串 {response_base64} 仅包含从客户端接收的实际响应。

   重要的是要注意，OpenVPN2 仅传递身份验证信息，不进行任何进一步检查。（例如，如果之前发出了 CR 或如果从客户端发送了多个 CR 响应或如果数据具有有效的 base64 编码）

   此接口应该足以满足几乎所有可以通过单轮和响应的 base64 编码实现的挑战/响应系统。需要多轮或更复杂答案的机制应实现不同于 CR_RESPONSE 的响应类型。


变量：

CID --  客户端 ID，每个连接客户端的数字 ID，序列 = 0,1,2,...
KID --  密钥 ID，与给定客户端 TLS 会话关联的密钥的数字 ID，序列 = 0,1,2,...
PRI --  主要 (1) 或次要 (0) VPN 地址/子网。所有客户端至少有一个主要 IP 地址。次要地址/子网与客户端特定的 "iroute" 指令关联。
ADDR -- IPv4 地址/子网，格式为 ******* 或 *******/*************

在极不可能的极长运行 OpenVPN 服务器场景中，CID 和 KID 应假定在 (2^32)-1 后回收到 0，但是这种回收行为保证是无冲突的。

命令解析
---------------

管理接口使用与 OpenVPN 配置文件解析器使用的相同命令行词法分析器。

空白是参数分隔符。

双引号或单引号字符（""、''）可用于包含包含空白的参数。

执行基于反斜杠的 shell 转义，使用以下映射，当不在单引号中时：

\\       映射到单个反斜杠字符 (\)。
\"       传递字面双引号字符 (")，不要将其解释为包含参数。
\[SPACE] 传递字面空格或制表符字符，不要将其解释为参数分隔符。

挑战/响应协议
---------------------------

OpenVPN 挑战/响应协议允许 OpenVPN 服务器生成向用户显示的挑战问题，并查看用户对这些挑战的响应。基于响应，服务器可以允许或拒绝访问。

该协议可用于实现多因素身份验证，因为用户除了用户名和密码外，还必须输入额外的信息才能成功进行身份验证。在多因素身份验证中，此信息用于证明用户拥有某个类似密钥的设备，如加密令牌或特定移动电话。

支持挑战/响应协议的两种变体："静态"和"动态"协议：

 * 静态协议使用 OpenVPN 的 "--static-challenge" 选项。

 * 动态协议不涉及特殊的 OpenVPN 选项或操作。它是服务器上的 auth-user-pass 验证过程与管理接口客户端之间的协议，在"验证失败"消息中使用以"['CRV1"开头的自定义字符串。（"["字符和消息末尾的匹配"]"字符由客户端 OpenVPN 程序添加，不存在于 auth-user-pass 验证过程生成的字符串或服务器发送的字符串中。）

动态协议：

OpenVPN 动态挑战/响应协议通过在初始成功身份验证后返回特殊格式的错误消息来工作。错误消息有两个目的：

 1. 它导致 OpenVPN 重新启动连接尝试。

 2. 它包含有关挑战的信息，应用于构造对下一个身份验证请求的响应（这将在重新启动后发生）。

注意：

 * '--auth-retry interact' 必须生效，以便重新启动连接并再次请求凭据。

 * '--auth-retry none'（这是默认值）将导致 OpenVPN 以致命错误退出而不重试，动态挑战/响应永远不会发生，因为不会发送"Need 'Auth' username/password"。

错误消息格式如下：

   >PASSWORD:Verification Failed: 'Auth' ['CRV1:<flags>:<state_id>:<username_base64>:<challenge_text>']

<flags>：一系列可选的、逗号分隔的标志：
 E：当用户键入响应时回显响应。
 R：需要响应。

<state_id>：应与响应一起返回给服务器的不透明字符串。

<username_base64>：编码为 base 64 的用户名。

<challenge_text>：要向用户显示的挑战文本。

<state_id> 不得包含冒号字符（":"），但 <challenge_text> 可以。

挑战示例：

  CRV1:R,E:Om01u7Fh4LrGBS7uh0SWmzwabUiGiW6l:Y3Ix:Please enter token PIN

下次使用以下方式请求用户名和密码时

   >PASSWORD:Need 'Auth' username/password

管理接口客户端应显示挑战文本，如果指定了 R 标志，则从用户获取响应。管理接口客户端应响应：

   username "Auth" <username>
   password "Auth" CRV1::<state_id>::<response_text>

其中 <username> 是从 <username_base64> 解码的用户名，<state_id> 取自挑战请求，<response_text> 是用户响应挑战输入的内容，可以是空字符串。如果不存在 R 标志，<response_text> 应该是空字符串。

（如上面"命令 -- password 和 username"部分中描述的所有用户名/密码响应一样，用户名和/或密码可以用引号括起来，特殊字符如双引号或反斜杠必须转义。有关更多信息，请参阅上面的"命令解析"部分。）

响应示例（假设用户为令牌 PIN 输入"8675309"）：

   username "Auth" cr1
   password "Auth" CRV1::Om01u7Fh4LrGBS7uh0SWmzwabUiGiW6l::8675309

（"Y3Ix"是"cr1"的 base 64 编码。）

静态协议：

静态协议与动态协议的不同之处在于，挑战问题在用户名/密码请求中发送给管理接口客户端，用户名、密码和响应在响应该请求时传递回服务器。

OpenVPN 的 --static-challenge 选项用于向 OpenVPN 提供挑战文本并指示是否应回显响应以及如何将响应与密码组合。

当需要凭据并使用 --static-challenge 选项时，管理接口将发送：

  >PASSWORD:Need 'Auth' username/password SC:<flag>,<TEXT>

  flag：一个整数，其最低有效位是 ECHO 标志，下一个有效位是 FORMAT 标志。
        ECHO = (flag & 0x1) 如果应回显响应则为 1，不回显则为 0
        FORMAT = (flag & 0x2) 如果响应应与密码作为纯文本连接则为 1，如果响应和密码应按下面描述的方式编码则为 0。因此 flag 可以取值 0、1、2 或 3。
  TEXT：应向用户显示以促进其响应的挑战文本

例如：

  >PASSWORD:Need 'Auth' username/password SC:1,Please enter token PIN

上述通知表示 OpenVPN 需要 --auth-user-pass 用户名和密码以及对静态挑战（"Please enter token PIN"）的响应。"SC:"后的"1"表示应回显响应。

在这种情况下，管理接口客户端应将静态挑战文本添加到身份验证对话框中，后跟用户输入响应的字段。如果 flag = 0 或 1（即，FORMAT=0），管理接口客户端应将密码和响应打包到编码密码中并发送：

  username "Auth" <username>
  password "Auth" "SCRV1:<password_base64>:<response_base64>"

其中 <username> 是用户输入的用户名，<password_base64> 是用户输入的密码的 base 64 编码，<response_base64> 是用户输入的响应的 base 64 编码。<password_base64> 和/或 <response_base64> 可以是空字符串。

如果 flag = 2 或 3（即，FORMAT=1），客户端应简单地连接密码和响应，不使用分隔符并发送：

  username "Auth" <username>
  password "Auth" "<password><response>"

（如上面"命令 -- password 和 username"部分中描述的所有用户名/密码响应一样，用户名可以用引号括起来，特殊字符如双引号或反斜杠必须转义。有关更多信息，请参阅上面的"命令解析"部分。）

例如，如果用户"foo"输入"bar"作为密码，8675309 作为 PIN，如果 flag = 0 或 1（即，FORMAT = 0），应发出以下管理接口命令：

  username "Auth" foo
  password "Auth" "SCRV1:YmFy:ODY3NTMwOQ=="

  （"YmFy"是"bar"的 base 64 编码，"ODY3NTMwOQ=="是"8675309"的 base 64 编码。）

或者，如果 flag = 2 或 3（即，FORMAT = 1）：

  username "Auth" foo
  password "Auth" "bar8675309"
