package me.meilon.vpnmgr.infrastructure.utils;

import jakarta.annotation.Nonnull;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpHeaders;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class HttpUtils {


    @SuppressWarnings("all")
    public static int copy(@Nonnull String fileDir, @Nonnull String fileName,
                           @Nonnull HttpServletResponse response){
        String filePath = FileUtil.unite(fileDir, fileName);
        File file = new File(filePath);
        return copy(file, response);
    }

    public static String[] getBasicAuth(@Nonnull HttpServletRequest request){
      String auth = request.getHeader(HttpHeaders.AUTHORIZATION);
      if (auth == null || !auth.startsWith("Basic ")) {
        return null;
      }
      String base64Credentials = auth.substring("Basic ".length());
      byte[] credDecoded = Base64.getDecoder().decode(base64Credentials);
      String credentials = new String(credDecoded, StandardCharsets.UTF_8);
      // credentials = username:password
      final String[] values = credentials.split(":", 2);
      if (values.length != 2) {
        return null;
      }
      return values;
    }

    public static int copyProfile(@Nonnull String fileDir, @Nonnull String fileName,
                                  @Nonnull HttpServletResponse response){
      response.reset();
      // 设置相关格式
      response.setContentType("application/x-openvpn-profile");
//      response.setContentType("application/x-openvpn-profile");
      response.setHeader(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
      response.setHeader(HttpHeaders.PRAGMA, "no-cache");
      response.setHeader(HttpHeaders.EXPIRES, "0");
      // 添加CORS头部支持跨域
      response.setHeader("Access-Control-Allow-Origin", "*");
      response.setHeader("Access-Control-Allow-Methods", "GET");
      response.setHeader("Access-Control-Allow-Headers", "*");
      // 设置下载后的文件名以及header
      response.addHeader("Content-disposition","attachment;fileName=profile.ovpn");


      String filePath = FileUtil.unite(fileDir, fileName);
      File file = new File(filePath);
      int byteCount = 0;
      try (FileInputStream in = new FileInputStream(file)) {
        OutputStream out = response.getOutputStream();
        int bytesRead;
        for (byte[] buffer = new byte[1024]; (bytesRead = in.read(buffer)) != -1; byteCount += bytesRead) {
          out.write(buffer, 0, bytesRead);
        }

        out.flush();
        return byteCount;
      } catch (FileNotFoundException e) {
        response.setStatus(404);
        throw new RuntimeException("要下载的文件不存在", e);
      } catch (IOException e) {
        response.setStatus(500);
        throw new RuntimeException("文件下载失败", e);
      }
    }

    public static int copy(@Nonnull File file, @Nonnull HttpServletResponse response) {
        response.reset();
        // 设置相关格式
        response.setContentType("application/force-download;charset=utf-8");
        // 设置下载后的文件名以及header
        response.addHeader("Content-disposition",
                "attachment;fileName=" + new String(file.getName().getBytes(),
                        StandardCharsets.ISO_8859_1));

        int byteCount = 0;
        try (FileInputStream in = new FileInputStream(file)) {
            OutputStream out = response.getOutputStream();
            int bytesRead;
            for (byte[] buffer = new byte[1024]; (bytesRead = in.read(buffer)) != -1; byteCount += bytesRead) {
                out.write(buffer, 0, bytesRead);
            }

            out.flush();
            return byteCount;
        } catch (FileNotFoundException e) {
            response.setStatus(404);
            throw new RuntimeException("要下载的文件不存在", e);
        } catch (IOException e) {
            response.setStatus(500);
            throw new RuntimeException("文件下载失败", e);
        }

    }

}
