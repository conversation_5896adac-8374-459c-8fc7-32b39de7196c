package me.meilon.vpnmgr.infrastructure.management;


import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.infrastructure.management.session.OVMSession;
import me.meilon.vpnmgr.infrastructure.management.v3.commands.Exit;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
public class OpenvpnManagementClient {

    private final OVMSession session;
    private OVMHandler handler;
    private OVMErrorHandler errorHandler;
    private Thread readThread;
    private final Map<Class<?>, AbstractFuture<? extends Message>> futureMap;

    public OpenvpnManagementClient(OVMSession session) {
        this(session, false);
    }

    public OpenvpnManagementClient(OVMSession session, boolean isReading) {
        this.session = session;
        this.futureMap = new ConcurrentHashMap<>();
        if (isReading) reading();
    }

    /**
     * 添加一个通用消息处理器
     * @param handler 消息处理器;
     *                注意: 该处理器会处理所有的消息, 无论是什么类型的消息都会触发
     *                如果你希望只处理指定类型的消息, 应使用 {@link #addParseHandler(OVMTypeHandler)}
     *                如果你希望在处理特定消息之外, 同时处理所有消息,
     *                可以同时使用当前方法以及 {@link #addParseHandler(OVMTypeHandler)},
     *                该处理器会在 {@link OVMTypeHandler#handle(Message)} 之后触发
     */
    public void addHandler(OVMHandler handler){
        this.handler = handler;
    }

    /**
     * 添加一个异常处理器
     * @param errorHandler 异常处理器
     *                     注意: 该处理器会区分异常类型,
     *                     如果是 IOException 则会调用 {@link OVMErrorHandler#handle(IOException)}
     *                     如果是其他异常则会调用 {@link OVMErrorHandler#handle(Throwable)}
     *                     IO异常通常情况下都是openvpn管理接口的连接问题;
     *                     其他异常通常情况下都是解析器或者处理器未处理的异常,
     *                     如果你希望统一处理这些异常, 可以重写 {@link OVMErrorHandler#handle(Throwable)} 方法.
     *                     否则建议在处理器内部自行处理异常
     */
    public void addErrorHandler(OVMErrorHandler errorHandler){
        this.errorHandler = errorHandler;
    }

    /**
     * 添加一个指定类型的消息处理器, 只有在接收到对应类型的时候才会触发
     * @param handler 消息处理器
     */
    public void addParseHandler(OVMTypeHandler<?> handler){
        session.getParserRepository().addHandler(handler);
    }

    /**
     * 发送一条命令到openvpn管理接口
     * @param command 命令对象
     */
    public void send(@Nonnull Command command) throws IOException {
        checkConnect();
        session.getWriter().send(command);
    }

    /**
     * 发送一条命令并且异步等待响应
     */
    public <M extends Message> OVMFuture<M> sendAndAsyncWait(@Nonnull Command command, Class<M> msgClass) throws IOException {
        checkConnect();
        AbstractFuture<M> future;
        if (msgClass == null){
            future = new NullFuture<>();
        }
        else {
            future = new CallFuture<>();
            futureMap.put(msgClass, future);
        }
        log.info("send command: {}", command.toCmdStr());
        session.getWriter().send(command);
        return future;
    }

    @Nonnull
    public <M extends Message> M sendAndWait(@Nonnull Command command, Class<M> msgClass) throws IOException {
        OVMFuture<M> future = sendAndAsyncWait(command, msgClass);
        return future.get(2, TimeUnit.SECONDS);
    }

    /**
     * 发送一条命令并且等待响应
     */
    @Nonnull
    public Message sendAndWait(@Nonnull Command command) throws IOException {
        Class<? extends Message> msgClass = session.getParserRepository()
                .getMessageMapClass(command.getClass());
        OVMFuture<? extends Message> future = sendAndAsyncWait(command, msgClass);
        return future.get(2, TimeUnit.SECONDS);
    }

    /**
     * 发送一条命令到openvpn管理接口
     * @param command 命令字符串
     */
    public void send(String command) throws IOException {
        checkConnect();
        session.getWriter().send(command);
    }

    /**
     * 读取一行响应消息
     * 注意: 对于存在多行的消息, 此方法读取到的是不完整的消息
     * @return 响应消息
     */
    public String readLine() throws IOException {
        checkConnect();
        return session.getReader().readLine();
    }

    /**
     * 读取一条响应消息
     * 在没有获取到一个完整的响应消息体之前，会一直阻塞
     * @return 响应消息
     */
    @Nonnull
    public Message readMessage() throws IOException {
        checkConnect();
        Message message = session.getReader().readMessage(cmd->{
            try {
                send(cmd);
            } catch (IOException e) {
                log.error("发送命令失败", e);
            }
        });
        AbstractFuture<?> callFuture = futureMap.get(message.getClass());
        if (callFuture != null){
            callFuture.setMessage(message);
        }
        return message;
    }

    private synchronized void reading(){
        if (this.readThread != null){
            return;
        }
//        this.readThread =new Thread(()->{
//            while (true){
//                try {
//                    Message response = readMessage();
//                    if (handler != null){
//                        handler.handle(response);
//                    }
//                    continue;
//                } catch (IOException e) {
//                    log.error("", e);
//                    if (errorHandler != null){
//                        errorHandler.handle0(e);
//                    }
//                } catch (Throwable e){
//                    log.error("", e);
//                    if (errorHandler != null){
//                        errorHandler.handle0(e);
//                    }
//                }
//                try {
//                    Thread.sleep(10_000);
//                } catch (InterruptedException ex) {
//                    log.error("", ex);
//                }
//            }
//        });
//        this.readThread.setDaemon(true);
//        this.readThread.setName("OVM-READER");
//        log.info("Start the read thread");
//        this.readThread.start();
    }

    public void checkConnect() throws IOException {
        if (session == null){
            throw new IOException("openvpn management interface connection is null");
        }
        if (session.isClosed()){
            session.connect();
        }
    }

    public synchronized void close() throws IOException {
        if (this.readThread != null){
            this.readThread.interrupt();
            this.readThread = null;
        }
        if (session.isConnected()) {
            session.getWriter().send(Exit.CMD);
            session.disconnect();
        }
    }
}
