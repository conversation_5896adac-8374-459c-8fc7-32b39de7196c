package me.meilon.vpnmgr.facade.http;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.websocket.server.PathParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.args.CerPageQry;
import me.meilon.vpnmgr.app.args.CertificateAdd;
import me.meilon.vpnmgr.app.dto.CertificateDto;
import me.meilon.vpnmgr.app.dto.base.PageResult;
import me.meilon.vpnmgr.app.dto.base.Result;
import me.meilon.vpnmgr.app.service.EasyRsaService;
import me.meilon.vpnmgr.infrastructure.biz.certificate.jpa.CertificateDo;
import me.meilon.vpnmgr.infrastructure.biz.certificate.jpa.CertificateRepository;
import me.meilon.vpnmgr.infrastructure.conf.OpenvpnProperty;
import me.meilon.vpnmgr.infrastructure.exception.ValidationFailed;
import me.meilon.vpnmgr.infrastructure.utils.DesUtil;
import me.meilon.vpnmgr.infrastructure.utils.HttpUtils;
import me.meilon.vpnmgr.infrastructure.utils.StrUtils;
import me.meilon.vpnmgr.infrastructure.utils.json.JsonUtil;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Base64;

/**
 * 证书管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/cer")
@RequiredArgsConstructor
public class EasyRsaController {

  private final CertificateRepository certificateRepository;
  private final EasyRsaService easyRsaService;
  private final OpenvpnProperty property;


  /**
   * 查询证书
   */
  @PostMapping("query")
  public PageResult<CertificateDto> query(@RequestBody CerPageQry queryCer) {
    log.info("/api/cer/query : {}", JsonUtil.toJsonString(queryCer));
    return PageResult.ofPath(easyRsaService.query(queryCer));
  }

  /**
   * 创建一个证书
   *
   * @param args 客户端证书参数
   * @return 创建好的证书基本信息
   */
  @PostMapping("create")
  public Result<CertificateDto> newClient(@RequestBody CertificateAdd args) {
    log.info("/api/cer/create : {}", JsonUtil.toJsonString(args));
    return Result.withData(easyRsaService.create(args));
  }

  /**
   * 作废一个VPN证书
   *
   * @param cn 客户端名称
   */
  @GetMapping("revokeClient")
  public Result<Void> revokeClient(@PathParam("cn") String cn) {
    log.info("/api/cer/revokeClient : {}", cn);
    if (cn == null || cn.isEmpty()) {
      return Result.err("CN 不能为空");
    }
    easyRsaService.revokeClient(cn);
    return Result.ok();
  }

  /**
   * 下载证书配置文件
   *
   * @param cn 客户端名称
   */
  @GetMapping("downConf")
  public void downConf(HttpServletResponse response, @PathParam("cn") String cn) {
    log.info("/api/cer/downConf : {}", cn);
    if (cn == null || cn.isEmpty()) {
      response.setStatus(403);
      throw new ValidationFailed("客户端名称不能为空");
    }
    String fileName = cn + ".ovpn";
    String clientDir = property.getClientSaveDir();
    HttpUtils.copy(clientDir, fileName, response);
  }

  @GetMapping("profile")
  public void profile(HttpServletResponse response, @PathParam("cn") String cn) {
    log.info("/api/cer/profile : {}", cn);
    if (cn == null || cn.isEmpty()) {
      response.setStatus(403);
      throw new ValidationFailed("客户端名称不能为空");
    }
    String fileName = "LX_staff_241128.ovpn";
    String clientDir = "C:\\Users\\<USER>\\Desktop\\";
    HttpUtils.copyProfile(clientDir, fileName, response);
  }

  @GetMapping("GetUserlogin")
  public ResponseEntity<String> getUserLogin(
      @RequestParam(name = "tls-cryptv2", required = false) String tlsCryptV2,
      @RequestParam(name = "deviceID", required = false) String deviceID,
      @RequestParam(name = "action", required = false) String action,
      @RequestParam(name = "username", required = false) String username,
      @RequestParam(name = "password", required = false) String password,
      HttpServletRequest request,
      HttpServletResponse response) {

    log.info("getUserLogin: tls-cryptv2={}, deviceID={}, action={}, username={}, password={}",
        tlsCryptV2, deviceID, action, username, password);

    // 设置CORS头
    response.setHeader("Access-Control-Allow-Origin", "*");
    response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
    response.setHeader("Access-Control-Allow-Headers", "*");

    HttpHeaders headers = new HttpHeaders();
    response.setHeader(HttpHeaders.CONTENT_TYPE, "text/plain; charset=utf-8");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setHeader("Pragma", "no-cache");
    response.setHeader("Expires", "0");

    StringBuilder responseBody = new StringBuilder();

    try {
      if ("import".equals(action)) {
        // 处理导入请求
        responseBody.append("SUCCESS:\n");
        responseBody.append("MESSAGE:Profile ready for download\n");
        responseBody.append("INFO,PROTOCOL:OpenVPN\n");
        responseBody.append("INFO,PROFILE_URL:").append(buildProfileDownloadUrl(request)).append("\n");
        responseBody.append("INFO,FRIENDLY_NAME:Your OpenVPN Server\n");
        responseBody.append("INFO,USERNAME_NEEDED:no\n");
        responseBody.append("INFO,PASSWORD_NEEDED:no\n");
        responseBody.append("INFO,SESSION_TOKEN:").append(generateSessionToken()).append("\n");
      } else if (username != null && password != null) {
        // 处理用户认证
        if (authenticateUser(username, password)) {
          responseBody.append("SUCCESS:\n");
          responseBody.append("MESSAGE:Authentication successful\n");
          responseBody.append("INFO,PROTOCOL:OpenVPN\n");
          responseBody.append("INFO,USERNAME:").append(username).append("\n");
          responseBody.append("INFO,PROFILE_URL:").append(buildUserProfileUrl(request, username)).append("\n");
        } else {
          responseBody.append("FAILED:Authentication failed\n");
          responseBody.append("MESSAGE:Invalid username or password\n");
        }
      } else {
        // 默认服务器信息响应
        responseBody.append("SUCCESS:\n");
        responseBody.append("MESSAGE:OpenVPN Access Server\n");
        responseBody.append("INFO,PROTOCOL:OpenVPN\n");
        responseBody.append("INFO,VERSION:2.9.0\n");
        responseBody.append("INFO,BUILD:OpenVPN Inc\n");
        responseBody.append("INFO,MODE:server\n");
      }

    } catch (Exception e) {
      responseBody = new StringBuilder();
      responseBody.append("FAILED:Internal server error\n");
      responseBody.append("MESSAGE:").append(e.getMessage()).append("\n");
    }

    return ResponseEntity.ok()
        .headers(headers)
        .body(responseBody.toString());
  }


  /**
   * 构建profile下载URL
   */
  private String buildProfileDownloadUrl(HttpServletRequest request) {
    return getBaseUrl(request) + "/api/cer/profile?cn=cn";
  }

  /**
   * 构建用户特定的profile URL
   */
  private String buildUserProfileUrl(HttpServletRequest request, String username) {
    return getBaseUrl(request) + "/rest/GetAutologin?username=" + username;
  }

  /**
   * 获取基础URL
   */
  private String getBaseUrl(HttpServletRequest request) {
    String scheme = request.getScheme();
    String serverName = request.getServerName();
    int serverPort = request.getServerPort();

    StringBuilder url = new StringBuilder();
    url.append(scheme).append("://").append(serverName);

    if ((scheme.equals("http") && serverPort != 80) ||
        (scheme.equals("https") && serverPort != 443)) {
      url.append(":").append(serverPort);
    }

    return url.toString();
  }

  /**
   * 生成会话令牌
   */
  private String generateSessionToken() {
    return Base64.getEncoder().encodeToString(
        ("session_" + System.currentTimeMillis()).getBytes()
    );
  }

  /**
   * 用户认证 - 根据实际需求实现
   */
  private boolean authenticateUser(String username, String password) {
    // 这里实现你的用户认证逻辑
    // 可以连接数据库、LDAP或其他认证系统

    // 示例：简单的用户名密码验证
    if ("admin".equals(username) && "password".equals(password)) {
      return true;
    }

    // 或者允许所有用户（仅用于测试）
    return username != null && !username.trim().isEmpty() &&
        password != null && !password.trim().isEmpty();
  }

  /**
   * OPTIONS 请求处理 - 支持 CORS 预检
   */
  @RequestMapping(value = "/options", method = RequestMethod.OPTIONS)
  public ResponseEntity<Void> handleOptions(HttpServletResponse response) {
    log.info("options request received");
    response.setHeader("Access-Control-Allow-Origin", "*");
    response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
    response.setHeader("Access-Control-Allow-Headers", "*");
    response.setHeader("Access-Control-Max-Age", "3600");
    return ResponseEntity.ok().build();
  }

  /**
   * 查看证书密码
   */
  @GetMapping("passwd")
  public Result<String> queryPasswd(@PathParam("cn") String cn) {
    CertificateDo cer = certificateRepository.findOneByClientName(cn)
        .orElse(new CertificateDo());
    String pwd = cer.getPassword();
    if (StrUtils.isBlank(pwd)) {
      return Result.withData("无密码");
    } else {
      return Result.withData(DesUtil.decrypt(pwd));
    }
  }

}
