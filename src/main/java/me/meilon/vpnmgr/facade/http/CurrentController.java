package me.meilon.vpnmgr.facade.http;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.args.ClientLinkRecordPageQry;
import me.meilon.vpnmgr.app.args.base.PageQry;
import me.meilon.vpnmgr.app.args.user.UserPasswdMod;
import me.meilon.vpnmgr.app.dto.UserDto;
import me.meilon.vpnmgr.app.dto.base.PageResult;
import me.meilon.vpnmgr.app.dto.base.Result;
import me.meilon.vpnmgr.app.dto.client.ConnRecord;
import me.meilon.vpnmgr.app.service.ClientService;
import me.meilon.vpnmgr.app.service.TotpService;
import me.meilon.vpnmgr.app.service.UserService;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserDo;
import me.meilon.vpnmgr.infrastructure.conf.OpenvpnProperty;
import me.meilon.vpnmgr.infrastructure.exception.ValidationFailed;
import me.meilon.vpnmgr.infrastructure.utils.HttpUtils;
import me.meilon.vpnmgr.infrastructure.web.AuthInterceptor;
import me.meilon.vpnmgr.infrastructure.web.Token;
import me.meilon.vpnmgr.infrastructure.web.TokenCache;
import org.springframework.web.bind.annotation.*;


/**
 * 当前用户管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/current")
@RequiredArgsConstructor
public class CurrentController {

    private final TotpService totpService;
    private final TokenCache tokenCache;
    private final OpenvpnProperty property;
    private final ClientService clientService;
    private final UserService userService;

    /**
     * 查询当前登录用户信息
     */
    @GetMapping(value = "queryUser")
    public Result<UserDto> current(HttpServletRequest request) {
        return Result.withData(getCurrentUser(request).toDto());
    }

    /**
     * 为当前登录用户创建QR码
     */
    @GetMapping("createQrCode")
    public Result<String> createQrCode(HttpServletRequest request){
        return Result.withData(totpService.createQrCode(getCurrentUser(request).getUserCode()));
    }

    /**
     * 验证当前登录用户的QR码
     * @param otp 动态码
     */
    @GetMapping("verifyQrCode")
    public Result<Void> checkQrCode(HttpServletRequest request, @RequestParam Integer otp){
        if (otp == null){
            return Result.err(-1, "验证码为空");
        }
        if (totpService.verifyQrCode(getCurrentUser(request).getUserCode(), otp)){
            return Result.ok("验证完成");
        }
        return Result.ok("验证失败");
    }

    /**
     * 关闭动态码
     * @param otp 动态码
     */
    @GetMapping("closeTotp")
    public Result<Void> closeTotp(HttpServletRequest request, @RequestParam Integer otp){
        SystemUserDo userDo = getCurrentUser(request);
        totpService.close(userDo.getUserCode(), otp);
        return Result.ok("关闭成功");
    }

    /**
     * 修改当前用户密码
     */
    @PostMapping("updatePasswd")
    public Result<Void> modPassword(HttpServletRequest request, @RequestBody UserPasswdMod args){
        SystemUserDo userDo = getCurrentUser(request);
        userService.updatePasswd(userDo.getId(), args);
        return Result.ok("修改密码成功");
    }

    /**
     * 下载当前登录用户的证书
     */
    @GetMapping("downConf")
    public void downConf(HttpServletRequest request, HttpServletResponse response) {
        String clientName = getCurrentUser(request).getCertificate();
        log.info("/api/cer/downConf : {}", clientName);
        if (clientName == null){
            response.setStatus(403);
            throw new ValidationFailed("没有可以下载的证书文件");
        }
        String fileName = clientName + ".ovpn";
        String clientDir = property.getClientSaveDir();
        HttpUtils.copy(clientDir, fileName, response);
    }

    /**
     * 查询当前用户的链接记录
     */
    @PostMapping("connRecord")
    public PageResult<ConnRecord> connectRecord(HttpServletRequest request,
                                                @RequestBody PageQry args){

        String userCode = getCurrentUser(request).getUserCode();
        ClientLinkRecordPageQry param = new ClientLinkRecordPageQry();
        param.setUserCode(userCode);
        param.setPageNumber(args.getPageNumber());
        param.setPageSize(args.getPageSize());
        return PageResult.ofPath(clientService.queryRecordPage(param));
    }


    private SystemUserDo getCurrentUser(HttpServletRequest request){
        String tokenId = request.getHeader(AuthInterceptor.AUTH_HEADER);
        Token token = tokenCache.getToken(tokenId);
        return token.getUser();
    }

}
