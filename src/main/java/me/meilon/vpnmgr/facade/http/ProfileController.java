package me.meilon.vpnmgr.facade.http;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.websocket.server.PathParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.service.EasyRsaService;
import me.meilon.vpnmgr.infrastructure.biz.certificate.jpa.CertificateRepository;
import me.meilon.vpnmgr.infrastructure.conf.OpenvpnProperty;
import me.meilon.vpnmgr.infrastructure.exception.ValidationFailed;
import me.meilon.vpnmgr.infrastructure.utils.HttpUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Base64;

/**
 * APIs that are related to the management of client profiles
 * openvpn 客户端使用
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class ProfileController {

  private final CertificateRepository certificateRepository;
  private final EasyRsaService easyRsaService;
  private final OpenvpnProperty property;

  /**
   * 该接口主要由 OpenVPN 客户端使用，以检查是否应使用基于网络的登录方法来导入配置文件。
   * 详细信息请参阅 OpenVPN webauth API 规范
   * <a href="https://github.com/OpenVPN/openvpn3/blob/master/doc/webauth.md#detection-of-web-based-profile-download-support">
   *   ...
   * </a>。
   */
  @RequestMapping(value = "/openvpn-api/profile", method = {RequestMethod.GET,RequestMethod.HEAD})
  public ResponseEntity<Void> handleOptions(HttpServletResponse response) {
    log.info("options request received");
    response.setHeader("Access-Control-Allow-Origin", "*");
    response.setHeader("Access-Control-Allow-Methods", "GET, HEAD");
    response.setHeader("Access-Control-Allow-Headers", "*");
    response.setHeader("Access-Control-Max-Age", "3600");
    return ResponseEntity.ok().build();
  }


  @GetMapping("/rest/GetUserlogin")
  public void getUserLogin(
      @RequestParam(name = "tls-cryptv2", required = false) String tlsCryptV2,
      @RequestParam(name = "deviceID", required = false) String deviceID,
      @RequestParam(name = "action", required = false) String action,
      @RequestParam(name = "CWS_PORT", required = false) String cwsPort,
      HttpServletRequest request,
      HttpServletResponse response) {


    String[] auth = HttpUtils.getBasicAuth(request);
    if (auth == null) {
      throw new ValidationFailed("缺少或无效的Authorization头");
    }
    String username = auth[0];
    String password = auth[1];

    log.info("getUserLogin: tls-cryptv2={}, deviceID={}, action={}, username={}, password={}, cwsPort={}",
        tlsCryptV2, deviceID, action, username, password, cwsPort);

    // 设置CORS头
    response.setHeader("Access-Control-Allow-Origin", "*");
    response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
    response.setHeader("Access-Control-Allow-Headers", "*");

    HttpHeaders headers = new HttpHeaders();
    response.setHeader(HttpHeaders.CONTENT_TYPE, "text/plain; charset=utf-8");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setHeader("Pragma", "no-cache");
    response.setHeader("Expires", "0");

    String fileName = "LX_staff_241128.ovpn";
    String clientDir = "C:\\Users\\<USER>\\Desktop\\";
    HttpUtils.copyProfile(clientDir, fileName, response);
  }


  /**
   * 构建profile下载URL
   */
  private String buildProfileDownloadUrl(HttpServletRequest request) {
    return getBaseUrl(request) + "/api/cer/profile?cn=cn";
  }

  /**
   * 构建用户特定的profile URL
   */
  private String buildUserProfileUrl(HttpServletRequest request, String username) {
    return getBaseUrl(request) + "/rest/GetAutologin?username=" + username;
  }

  /**
   * 获取基础URL
   */
  private String getBaseUrl(HttpServletRequest request) {
    String scheme = request.getScheme();
    String serverName = request.getServerName();
    int serverPort = request.getServerPort();

    StringBuilder url = new StringBuilder();
    url.append(scheme).append("://").append(serverName);

    if ((scheme.equals("http") && serverPort != 80) ||
        (scheme.equals("https") && serverPort != 443)) {
      url.append(":").append(serverPort);
    }

    return url.toString();
  }

  /**
   * 生成会话令牌
   */
  private String generateSessionToken() {
    return Base64.getEncoder().encodeToString(
        ("session_" + System.currentTimeMillis()).getBytes()
    );
  }

  /**
   * 用户认证 - 根据实际需求实现
   */
  private boolean authenticateUser(String username, String password) {
    // 这里实现你的用户认证逻辑
    // 可以连接数据库、LDAP或其他认证系统

    // 示例：简单的用户名密码验证
    if ("admin".equals(username) && "password".equals(password)) {
      return true;
    }

    // 或者允许所有用户（仅用于测试）
    return username != null && !username.trim().isEmpty() &&
        password != null && !password.trim().isEmpty();
  }


}
