package me.meilon.vpnmgr.app.service;

import jakarta.annotation.Nonnull;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.meilon.vpnmgr.app.args.ClientLinkRecordPageQry;
import me.meilon.vpnmgr.app.args.ConnStatusPageQry;
import me.meilon.vpnmgr.app.dto.AuthResult;
import me.meilon.vpnmgr.app.dto.AuthState;
import me.meilon.vpnmgr.app.dto.client.ConnRecord;
import me.meilon.vpnmgr.app.dto.client.ConnStatus;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.enums.ConnRecordType;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.enums.ConnState;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.ClientLinkRecordDo;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.ClientLinkRecordRepository;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.ClientLinkStatusDo;
import me.meilon.vpnmgr.infrastructure.biz.openvpn.jpa.ClientLinkStatusRepository;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserDo;
import me.meilon.vpnmgr.infrastructure.biz.systemUser.jpa.SystemUserRepository;
import me.meilon.vpnmgr.infrastructure.exception.ValidationFailed;
import me.meilon.vpnmgr.infrastructure.management.Command;
import me.meilon.vpnmgr.infrastructure.management.OpenvpnManagementClient;
import me.meilon.vpnmgr.infrastructure.management.v3.commands.*;
import me.meilon.vpnmgr.infrastructure.management.v3.message.*;
import me.meilon.vpnmgr.infrastructure.utils.DateUtil;
import me.meilon.vpnmgr.infrastructure.utils.PageUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ClientService {


  private final AuthService authService;
  private final IpLeasesService leasesService;
  private final UserConfigService userConfigService;
  private final OpenvpnManagementClient client;
  private final SystemUserRepository userRepository;
  private final ClientLinkStatusRepository statusRepository;
  private final ClientLinkRecordRepository recordRepository;


//  @PostConstruct
  public void init() {
    try {
      log.info("初始化客户端连接状态");
      StatusMessage message = client.sendAndWait(Status.of(3), StatusMessage.class);
      List<ClientStatus> clientList = message.getClientList();
      for (ClientStatus item : clientList) {
        String userCode = item.getUsername();
        statusRepository.findOneByUserCode(userCode).ifPresent(statusDo -> {
          if (!item.getClientId().equals(statusDo.getClientId()) ||
                  statusDo.getStatus() != ConnState.CONNECT) {
            log.info("客户端[{}]连接状态异常，进行修复", userCode);
            statusDo.setClientIp(item.getVirtualAddress());
            statusDo.setTrustedAddress(item.getRealAddress());
            statusDo.setConnectTime(DateUtil.toLocalDateTime(item.getConnectedSinceTimeStamps()));
            statusDo.setClientId(item.getClientId());
            statusDo.setStatus(ConnState.CONNECT);
            statusRepository.save(statusDo);
          }
        });
      }
    } catch (IOException e) {
      log.error("", e);
    }
  }

  /**
   * 查询用户链接状态
   */
  public Page<ConnStatus> queryClientLinkStatus(ConnStatusPageQry args) {
    Sort sort = Sort.by(Sort.Direction.DESC, "connectTime");
    Pageable pageable = PageUtils.of(args, sort);
    return statusRepository.queryPage(pageable, args)
            .map(ClientLinkStatusDo::toDto);
  }

  /**
   * 查询用户链接记录
   */
  public Page<ConnRecord> queryRecordPage(@Nonnull ClientLinkRecordPageQry args) {
    Sort sort = Sort.by(Sort.Direction.DESC, "connectTime");
    Pageable pageable = PageUtils.of(args, sort);
    return recordRepository.query(pageable, args)
            .map(ClientLinkRecordDo::toEntity);
  }

  /**
   * 驱逐指定的客户端
   */
  public KillMessage evictClient(@Nonnull String id) {
    ClientLinkStatusDo status = statusRepository.findById(id)
            .orElseThrow(() -> new ValidationFailed("未找到客户端"));
    try {
      return client.sendAndWait(Kill.of(status.getUserCode()), KillMessage.class);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * 客户端连接时调用</br>
   * 进行鉴权判断
   */
  public Command onConnect(ClientEvent client) {
    String userCode = client.getUsername();
    Long clientIp = client.getCid();
    Long kid = client.getKid();
    if (requiredIsNull(client)){
      log.warn("无效的连接信息:\n{}", client.toStr());
      return ClientDeny.of(clientIp, kid, AuthState.AUTH_FAILED.name(), "用户名或密码错误");
    }

    if (log.isDebugEnabled()){
      log.debug("连接请求:\n{}", client.toStr());
    }
    SystemUserDo userDo = userRepository.findOneByUserCode(userCode)
        .orElse(null);
    String userName = userDo == null ? "未知用户" : userDo.getUserName();

    AuthResult authResult = authService.verify(userDo, client);
    return switch (authResult.getState()){
      case AUTH_SUCCESS:
        // 认证成功后判断是否需要重新分配IP
        log.info("用户 [{}] 连接成功, IP: {}, kid: {}", userName, clientIp, kid);
        if (userDo != null && leasesService.checkIpAboutToExpire(userCode)) {
          leasesService.allocateIp(userDo);
          userConfigService.syncUserConfig(userDo);
        }
        yield ClientAuthNt.of(clientIp, kid);
      case AUTH_FAILED:
        // 添加认证失败的记录
        ClientLinkRecordDo recordDo = ClientLinkRecordDo.create(client, userName, ConnRecordType.AUTH_FAILED);
        recordRepository.save(recordDo);
      case AUTH_CHALLENGE:
        yield ClientDeny.of(clientIp, kid, authResult);
    };
  }

  /**
   * 链接成功时调用</br>
   * 更新连接状态, 添加连接记录
   */
  @Transactional(rollbackFor = Throwable.class)
  public void onEstablished(ClientEvent client) {
    String userCode = client.getUsername();
    String sessionId = client.getSessionId();
    log.info("user [{}] sessionId: {}, {}, connect established",userCode, sessionId, client.getCid());

    SystemUserDo userDo = userRepository.findOneByUserCode(userCode)
            .orElse(null);
    String userName = userDo == null ? "未知用户" : userDo.getUserName();
    ConnRecordType type;
    ClientLinkStatusDo statusDo = statusRepository.findOneByUserCode(userCode)
        .orElse(null);

    if (statusDo == null){
      type = ConnRecordType.ESTABLISHED;
      // 如果连接状态为空, 则创建新连接状态
      statusDo = ClientLinkStatusDo.create(client, userName);
    }
    else {
      // 如果sessionId相同, 则认为是断线重连
      type = sessionId.equals(statusDo.getSessionId()) ? ConnRecordType.RECONNECTED : ConnRecordType.ESTABLISHED;
      statusDo.update(client, ConnState.CONNECT);

    }
    statusRepository.save(statusDo);
    // 添加连接记录
    ClientLinkRecordDo recordDo = ClientLinkRecordDo.create(statusDo, type);
    recordRepository.save(recordDo);
  }


  /**
   * 客户端断开连接调用</br>
   * 更新连接状态, 添加连接记录
   */
  @Transactional(rollbackFor = Throwable.class)
  public void onDisconnect(ClientEvent client) {
    String userCode = client.getUsername();
    String sessionId = client.getSessionId();
    if (sessionId == null || sessionId.isEmpty()) {
      return;
    }
    log.info("user [{}] sessionId: {}, {}, disconnect", userCode, sessionId, client.getCid());
    ClientLinkStatusDo statusDo = statusRepository.findOneByUserCode(userCode)
        .orElse(null);

    String userName = statusDo == null ? "未知用户" : statusDo.getUserName();
    if (statusDo != null && sessionId.equals(statusDo.getSessionId())) {
      // sessionId相同时更新状态
      statusDo.setStatus(ConnState.DISCONNECT);
      statusDo.setDisconnectTime(LocalDateTime.now());
      statusRepository.save(statusDo);
    }
    // 添加连接记录
    ClientLinkRecordDo recordDo = ClientLinkRecordDo.create(client, userName, ConnRecordType.DISCONNECTED);
    recordDo.setDisconnectTime(LocalDateTime.now());
    recordRepository.save(recordDo);
  }


  /**
   * 校验客户端事件必要参数是否为null
   * @param client 客户端事件
   * @return true: 有null值; false: 没有null值
   */
  private boolean requiredIsNull(ClientEvent client) {
    return client.getCid() == null
        || client.getKid() == null
        || client.getCommonName() == null
        || client.getUsername() == null
        || client.getPassword() == null
        || client.getSessionId() == null;
  }


}
