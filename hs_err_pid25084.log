#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_UNCAUGHT_CXX_EXCEPTION (0xe06d7363) at pc=0x00007ff9aa38001c, pid=25084, tid=28664
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.7+6-1038.58-jcef (21.0.7+6) (build 21.0.7+6-b1038.58)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.7+6-1038.58-jcef (21.0.7+6-b1038.58, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# Problematic frame:
# C  [KERNELBASE.dll+0x6001c]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://youtrack.jetbrains.com/issues/JBR
#

---------------  S <PERSON> M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'http://*************': 

Host: 12th Gen Intel(R) Core(TM) i9-12900H, 20 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Fri Sep 12 16:57:31 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5415) elapsed time: 0.084486 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000246e6d66ee0):  JavaThread "main"             [_thread_in_vm, id=28664, stack(0x0000008d0c900000,0x0000008d0ca00000) (1024K)]

Stack: [0x0000008d0c900000,0x0000008d0ca00000],  sp=0x0000008d0c9fdea0,  free space=1015k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [KERNELBASE.dll+0x6001c]
C  [VCRUNTIME140.dll+0x6480]
C  [jimage.dll+0x32a3]
C  [jimage.dll+0x2a11]
C  [jimage.dll+0x145b]
C  [jimage.dll+0x1e87]
C  [jimage.dll+0x1f1e]
C  [jimage.dll+0x265a]
V  [jvm.dll+0x225ea5]
V  [jvm.dll+0x224ca2]
V  [jvm.dll+0x83ccac]
V  [jvm.dll+0x83dd42]
V  [jvm.dll+0x83e304]
V  [jvm.dll+0x83df98]
V  [jvm.dll+0x27633b]
V  [jvm.dll+0x3e474e]
C  0x00000246f21ca97f

The last pc belongs to new (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  jdk.internal.module.ServicesCatalog.addProviders(Ljava/lang/String;[Ljdk/internal/module/ServicesCatalog$ServiceProvider;)V+18 java.base@21.0.7
j  jdk.internal.module.ServicesCatalog.register(Ljava/lang/Module;)V+111 java.base@21.0.7
j  java.lang.Module.defineModules(Ljava/lang/module/Configuration;Ljava/util/function/Function;Ljava/lang/ModuleLayer;)Ljava/util/Map;+747 java.base@21.0.7
j  java.lang.ModuleLayer.<init>(Ljava/lang/module/Configuration;Ljava/util/List;Ljava/util/function/Function;)V+34 java.base@21.0.7
j  java.lang.ModuleLayer.defineModules(Ljava/lang/module/Configuration;Ljava/util/List;Ljava/util/function/Function;)Ljava/lang/ModuleLayer$Controller;+36 java.base@21.0.7
j  java.lang.ModuleLayer.defineModules(Ljava/lang/module/Configuration;Ljava/util/function/Function;)Ljava/lang/ModuleLayer;+6 java.base@21.0.7
j  jdk.internal.module.ModuleBootstrap.boot2()Ljava/lang/ModuleLayer;+1207 java.base@21.0.7
j  jdk.internal.module.ModuleBootstrap.boot()Ljava/lang/ModuleLayer;+64 java.base@21.0.7
j  java.lang.System.initPhase2(ZZ)I+0 java.base@21.0.7
v  ~StubRoutines::call_stub 0x00000246f21b100d

siginfo: EXCEPTION_UNCAUGHT_CXX_EXCEPTION (0xe06d7363), ExceptionInformation=0x0000000019930520 0x0000008d0c9fe000 0x00007ff983d15198 0x00007ff983d10000 


Registers:
RAX=0x0000024687f70000, RBX=0x00007ff983d15198, RCX=0x0000000000000000, RDX=0x00007ff8c2378ec1
RSP=0x0000008d0c9fdea0, RBP=0x0000008d0c9fe130, RSI=0x0000008d0c9fe000, RDI=0x0000000019930520
R8 =0x00007ff8c1d60ed6, R9 =0x0000000000000000, R10=0x0000000000000072, R11=0x00000246e6d66ee0
R12=0x000000000000567e, R13=0x00000246e6d66ee0, R14=0x00000246881c5720, R15=0x00000246881c5720
RIP=0x00007ff9aa38001c, EFLAGS=0x0000000000000206

XMM[0]=0x00000246881af468 0x0000000000000000
XMM[1]=0x0000008d0c9fe1f8 0x0000008d0c9fe208
XMM[2]=0x00007ff9aaaef532 0x0000000000000039
XMM[3]=0x0000000000000040 0x0000000000000039
XMM[4]=0x000002468817ac20 0x0000000001000001
XMM[5]=0x0000000000000000 0x0000008d0c9fe200
XMM[6]=0x0000000000000000 0x0000000000000000
XMM[7]=0x0000000000000000 0x0000000000000000
XMM[8]=0x0000000000000000 0x0000000000000000
XMM[9]=0x0000000000000000 0x0000000000000000
XMM[10]=0x0000000000000000 0x0000000000000000
XMM[11]=0x0000000000000000 0x0000000000000000
XMM[12]=0x0000000000000000 0x0000000000000000
XMM[13]=0x0000000000000000 0x0000000000000000
XMM[14]=0x0000000000000000 0x0000000000000000
XMM[15]=0x0000000000000000 0x0000000000000000
  MXCSR=0x00001fa0


Register to memory mapping:

RAX=0x0000024687f70000 points into unknown readable memory: 0x6f547379654b6563 | 63 65 4b 65 79 73 54 6f
RBX=0x00007ff983d15198 jimage.dll
RCX=0x0 is null
RDX=0x00007ff8c2378ec1 jvm.dll
RSP=0x0000008d0c9fdea0 is pointing into the stack for thread: 0x00000246e6d66ee0
RBP=0x0000008d0c9fe130 is pointing into the stack for thread: 0x00000246e6d66ee0
RSI=0x0000008d0c9fe000 is pointing into the stack for thread: 0x00000246e6d66ee0
RDI=0x0000000019930520 is an unknown value
R8 =0x00007ff8c1d60ed6 jvm.dll
R9 =0x0 is null
R10=0x0000000000000072 is an unknown value
R11=0x00000246e6d66ee0 is a thread
R12=0x000000000000567e is an unknown value
R13=0x00000246e6d66ee0 is a thread
R14=0x00000246881c5720 points into unknown readable memory: 0x00000000ffffffff | ff ff ff ff 00 00 00 00
R15=0x00000246881c5720 points into unknown readable memory: 0x00000000ffffffff | ff ff ff ff 00 00 00 00

Top of Stack: (sp=0x0000008d0c9fdea0)
0x0000008d0c9fdea0:   00009c2d14d93b07 00007ff983d15198
0x0000008d0c9fdeb0:   0000008d0c9fe000 0000008d0c9fded0
0x0000008d0c9fdec0:   00000081e06d7363 0000000000000000
0x0000008d0c9fded0:   00007ff9aa38001c 0000000000000004
0x0000008d0c9fdee0:   0000000019930520 0000008d0c9fe000
0x0000008d0c9fdef0:   00007ff983d15198 00007ff983d10000
0x0000008d0c9fdf00:   0000024600000000 0000024600000002
0x0000008d0c9fdf10:   0000000000000000 00007ff983d15198
0x0000008d0c9fdf20:   00000246881c5720 00007ff9ad086c6f
0x0000008d0c9fdf30:   0000008d0c9fdfa0 00007ff983d17000
0x0000008d0c9fdf40:   0000008d0c9fe000 0000000019930520
0x0000008d0c9fdf50:   00007ff983d17000 00007ff983d10000
0x0000008d0c9fdf60:   0000f9c332da4c86 00007ff9aa348ddb
0x0000008d0c9fdf70:   00007ff983d15198 00007ff973c86480
0x0000008d0c9fdf80:   000019f480b3c9a9 0000008d0c9fe100
0x0000008d0c9fdf90:   0000000000000008 00007ff9aaaf0475
0x0000008d0c9fdfa0:   00007ff983d10000 0000000019930520
0x0000008d0c9fdfb0:   0000008d0c9fe000 00007ff983d15198
0x0000008d0c9fdfc0:   00007ff983d10000 00007ff9aab4a649
0x0000008d0c9fdfd0:   00000246e7519325 00007ff983d132a3
0x0000008d0c9fdfe0:   0000000000000000 00000246e7519325
0x0000008d0c9fdff0:   000000000000567e 00000246e7519325
0x0000008d0c9fe000:   00007ff983d14468 00007ff983d14478
0x0000008d0c9fe010:   0000000000000000 0000000800030000
0x0000008d0c9fe020:   00000246e7519325 00007ff983d12a11
0x0000008d0c9fe030:   000000000000567e 00000246894055c4
0x0000008d0c9fe040:   0000000c00000000 00007ff800000002
0x0000008d0c9fe050:   00000246e7519325 00007ff983d1145b
0x0000008d0c9fe060:   0000000200000001 0010000f00000001
0x0000008d0c9fe070:   0000001200000010 0000000000000000
0x0000008d0c9fe080:   0000008d0c9f0000 0000000000000020
0x0000008d0c9fe090:   00000000cafefafa 00000000000025dd 

Instructions: (pc=0x00007ff9aa38001c)
0x00007ff9aa37ff1c:   ff 4c 8d 05 f4 2a 2f 00 33 d2 b9 00 00 10 00 e8
0x00007ff9aa37ff2c:   b0 37 fe ff 4c 8b f0 48 89 44 24 58 48 85 c0 0f
0x00007ff9aa37ff3c:   84 0b fe ff ff 4c 8d 05 b8 2a 2f 00 33 d2 8d 4a
0x00007ff9aa37ff4c:   02 e8 8e 37 fe ff 48 8b f8 48 89 44 24 50 e9 ed
0x00007ff9aa37ff5c:   fd ff ff 45 33 c0 49 8b d7 48 83 c9 ff e8 c2 a5
0x00007ff9aa37ff6c:   01 00 e9 08 ff ff ff 48 8b 0d 7e 0e 32 00 e8 01
0x00007ff9aa37ff7c:   ba fd ff 48 8b cf e8 99 8e fc ff e9 f8 fe ff ff
0x00007ff9aa37ff8c:   49 8b ce e8 8c 8e fc ff e9 f4 fe ff ff 48 8b ce
0x00007ff9aa37ff9c:   e8 7f 8e fc ff e9 f0 fe ff ff cc cc 71 18 d3 7a
0x00007ff9aa37ffac:   1a 87 89 f9 48 81 ec d8 00 00 00 48 8b 05 32 e0
0x00007ff9aa37ffbc:   31 00 48 33 c4 48 89 84 24 c0 00 00 00 48 83 64
0x00007ff9aa37ffcc:   24 28 00 48 8d 05 da ff ff ff 83 e2 01 89 4c 24
0x00007ff9aa37ffdc:   20 89 54 24 24 48 89 44 24 30 4d 85 c9 74 4f b8
0x00007ff9aa37ffec:   0f 00 00 00 48 8d 4c 24 40 44 3b c0 49 8b d1 41
0x00007ff9aa37fffc:   0f 46 c0 44 8b c0 44 89 44 24 38 49 c1 e0 03 e8
0x00007ff9aa38000c:   c7 1d 09 00 48 8d 4c 24 20 48 ff 15 24 45 22 00
0x00007ff9aa38001c:   0f 1f 44 00 00 48 8b 8c 24 c0 00 00 00 48 33 cc
0x00007ff9aa38002c:   e8 2f 17 05 00 48 81 c4 d8 00 00 00 c3 cc 83 64
0x00007ff9aa38003c:   24 38 00 eb cf cc cc cc cc cc cc cc 48 8b c4 48
0x00007ff9aa38004c:   89 58 08 48 89 70 10 48 89 78 18 4c 89 68 20 55
0x00007ff9aa38005c:   41 56 41 57 48 8d 68 a1 48 81 ec e0 00 00 00 48
0x00007ff9aa38006c:   8b 05 7e df 31 00 48 33 c4 48 89 45 3f 45 33 ff
0x00007ff9aa38007c:   66 c7 45 2b 00 05 48 8d 45 c7 44 89 7d 27 48 89
0x00007ff9aa38008c:   44 24 50 48 8d 4d 27 44 89 7c 24 48 45 33 c9 44
0x00007ff9aa38009c:   89 7c 24 40 45 8d 47 12 44 89 7c 24 38 b2 01 44
0x00007ff9aa3800ac:   89 7c 24 30 41 8b df 44 89 7c 24 28 41 8b ff 44
0x00007ff9aa3800bc:   89 7c 24 20 45 8b f7 44 89 7d 2f 66 c7 45 33 00
0x00007ff9aa3800cc:   01 44 89 7d 37 66 c7 45 3b 00 10 4c 89 7d c7 4c
0x00007ff9aa3800dc:   89 7d cf 4c 89 7d d7 4c 89 7d df 48 ff 15 72 45
0x00007ff9aa3800ec:   22 00 0f 1f 44 00 00 85 c0 0f 88 a7 02 00 00 48
0x00007ff9aa3800fc:   8d 45 cf 41 b9 20 02 00 00 48 89 44 24 50 45 8d
0x00007ff9aa38010c:   6f 02 44 89 7c 24 48 45 8d 47 20 44 89 7c 24 40 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x00009c2d14d93b07 is an unknown value
stack at sp + 1 slots: 0x00007ff983d15198 jimage.dll
stack at sp + 2 slots: 0x0000008d0c9fe000 is pointing into the stack for thread: 0x00000246e6d66ee0
stack at sp + 3 slots: 0x0000008d0c9fded0 is pointing into the stack for thread: 0x00000246e6d66ee0
stack at sp + 4 slots: 0x00000081e06d7363 is an unknown value
stack at sp + 5 slots: 0x0 is null
stack at sp + 6 slots: 0x00007ff9aa38001c KERNELBASE.dll
stack at sp + 7 slots: 0x0000000000000004 is an unknown value

new  187 new  [0x00000246f21ca800, 0x00000246f21caa08]  520 bytes
[MachCode]
  0x00000246f21ca800: 4883 ec08 | c5fa 1104 | 24eb 1f48 | 83ec 10c5 | fb11 0424 | eb14 4883 | ec10 4889 | 0424 48c7 
  0x00000246f21ca820: 4424 0800 | 0000 00eb | 0150 410f | b755 010f | cac1 ea10 | 488b 4de8 | 488b 4908 | 488b 4908 
  0x00000246f21ca840: 488b 4108 | 807c 1004 | 070f 85d3 | 0000 0066 | 8b54 d148 | 488b 4928 | 488b 4cd1 | 0851 80b9 
  0x00000246f21ca860: 5101 0000 | 040f 85b6 | 0000 008b | 5108 f6c2 | 010f 85aa | 0000 0049 | 8b87 b801 | 0000 488d 
  0x00000246f21ca880: 1c10 493b | 9fc8 0100 | 000f 8792 | 0000 0049 | 899f b801 | 0000 4883 | ea10 0f84 | 0f00 0000 
  0x00000246f21ca8a0: 33c9 c1ea | 0348 894c | d008 48ff | ca75 f648 | c700 0100 | 0000 5933 | f689 700c | 49ba 0000 
  0x00000246f21ca8c0: 0000 0800 | 0000 492b | ca89 4808 | 49ba ff8e | 95c2 f87f | 0000 4180 | 3a00 0f84 | 3c00 0000 
  0x00000246f21ca8e0: 5048 8bc8 | 4883 ec20 | 40f6 c40f | 0f84 1900 | 0000 4883 | ec08 48b8 | a0ef 3ec2 | f87f 0000 
  0x00000246f21ca900: ffd0 4883 | c408 e90c | 0000 0048 | b8a0 ef3e | c2f8 7f00 | 00ff d048 | 83c4 2058 | e9cb 0000 
  0x00000246f21ca920: 0059 488b | 55e8 488b | 5208 488b | 5208 450f | b745 0141 | 0fc8 41c1 | e810 e805 | 0000 00e9 
  0x00000246f21ca940: a800 0000 | 488d 4424 | 084c 896d | c049 8bcf | c5f8 7749 | 89af a803 | 0000 4989 | 8798 0300 
  0x00000246f21ca960: 0048 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 | 83ec 0848 | b800 4708 | c2f8 7f00 | 00ff d048 
  0x00000246f21ca980: 83c4 08e9 | 0c00 0000 | 48b8 0047 | 08c2 f87f | 0000 ffd0 | 4883 c420 | 49c7 8798 | 0300 0000 
  0x00000246f21ca9a0: 0000 0049 | c787 a803 | 0000 0000 | 0000 49c7 | 87a0 0300 | 0000 0000 | 00c5 f877 | 4983 7f08 
  0x00000246f21ca9c0: 000f 8405 | 0000 00e9 | 3465 feff | 498b 87f0 | 0300 0049 | c787 f003 | 0000 0000 | 0000 4c8b 
  0x00000246f21ca9e0: 6dc0 4c8b | 75c8 4e8d | 74f5 00c3 | 410f b65d | 0349 83c5 | 0349 ba40 | 5998 c2f8 | 7f00 0041 
  0x00000246f21caa00: ff24 da0f | 1f44 0000 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000024688117ca0, length=11, elements={
0x00000246e6d66ee0, 0x0000024687ff4b70, 0x000002468801b790, 0x00000246880644c0,
0x0000024688081c90, 0x00000246880826f0, 0x0000024688083150, 0x0000024688075d30,
0x0000024688076ae0, 0x0000024688077b90, 0x000002468817d370
}

Java Threads: ( => current thread )
=>0x00000246e6d66ee0 JavaThread "main"                              [_thread_in_vm, id=28664, stack(0x0000008d0c900000,0x0000008d0ca00000) (1024K)]
  0x0000024687ff4b70 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=34360, stack(0x0000008d0d100000,0x0000008d0d200000) (1024K)]
  0x000002468801b790 JavaThread "Reference Handler"          daemon [_thread_blocked, id=38320, stack(0x0000008d0d200000,0x0000008d0d300000) (1024K)]
  0x00000246880644c0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=18876, stack(0x0000008d0d300000,0x0000008d0d400000) (1024K)]
  0x0000024688081c90 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=16168, stack(0x0000008d0d400000,0x0000008d0d500000) (1024K)]
  0x00000246880826f0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=36308, stack(0x0000008d0d500000,0x0000008d0d600000) (1024K)]
  0x0000024688083150 JavaThread "Service Thread"             daemon [_thread_blocked, id=21544, stack(0x0000008d0d600000,0x0000008d0d700000) (1024K)]
  0x0000024688075d30 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=11044, stack(0x0000008d0d700000,0x0000008d0d800000) (1024K)]
  0x0000024688076ae0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=12180, stack(0x0000008d0d800000,0x0000008d0d900000) (1024K)]
  0x0000024688077b90 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=33324, stack(0x0000008d0d900000,0x0000008d0da00000) (1024K)]
  0x000002468817d370 JavaThread "C1 CompilerThread1"         daemon [_thread_blocked, id=36360, stack(0x0000008d0da00000,0x0000008d0db00000) (1024K)]
Total: 11

Other Threads:
  0x0000024687fb34e0 VMThread "VM Thread"                           [id=38080, stack(0x0000008d0d000000,0x0000008d0d100000) (1024K)]
  0x0000024687f409d0 WatcherThread "VM Periodic Task Thread"        [id=37300, stack(0x0000008d0cf00000,0x0000008d0d000000) (1024K)]
  0x00000246fc9306f0 WorkerThread "GC Thread#0"                     [id=37580, stack(0x0000008d0ca00000,0x0000008d0cb00000) (1024K)]
  0x00000246e6ddb480 ConcurrentGCThread "G1 Main Marker"            [id=36452, stack(0x0000008d0cb00000,0x0000008d0cc00000) (1024K)]
  0x00000246e6ddbe90 WorkerThread "G1 Conc#0"                       [id=37164, stack(0x0000008d0cc00000,0x0000008d0cd00000) (1024K)]
  0x00000246fc9ead10 ConcurrentGCThread "G1 Refine#0"               [id=34504, stack(0x0000008d0cd00000,0x0000008d0ce00000) (1024K)]
  0x00000246e4826270 ConcurrentGCThread "G1 Service"                [id=35728, stack(0x0000008d0ce00000,0x0000008d0cf00000) (1024K)]
Total: 7

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000605000000, size: 8112 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 20 total, 20 available
 Memory: 32435M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8112M
 Pre-touch: Disabled
 Parallel Workers: 15
 Concurrent Workers: 4
 Concurrent Refinement Workers: 15
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 520192K, used 0K [0x0000000605000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 0 survivors (0K)
 Metaspace       used 4471K, committed 4544K, reserved 1114112K
  class space    used 353K, committed 384K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|   1|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|   2|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|   3|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|   4|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|   5|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|   6|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|   7|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|   8|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|   9|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  10|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  11|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  12|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  13|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  14|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  15|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  16|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  17|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  18|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  19|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  20|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  21|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  22|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  23|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  24|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  25|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  26|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  27|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  28|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  29|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  30|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  31|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  32|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  33|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  34|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  35|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  36|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  37|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  38|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  39|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  40|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  41|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  42|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  43|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  44|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  45|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  46|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  47|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  48|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  49|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  50|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  51|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  52|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  53|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  54|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  55|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  56|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  57|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  58|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  59|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  60|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  61|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  62|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  63|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  64|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  65|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  66|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  67|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  68|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  69|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  70|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  71|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  72|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  73|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  74|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  75|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  76|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  77|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  78|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  79|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  80|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  81|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  82|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  83|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  84|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  85|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  86|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  87|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  88|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  89|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  90|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  91|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  92|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  93|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  94|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
|  95|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
|  96|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
|  97|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
|  98|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
|  99|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 100|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 101|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 102|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 103|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 104|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 105|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 106|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 107|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 108|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 109|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 110|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 111|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 112|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 113|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 114|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 115|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 116|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 117|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 118|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Untracked 
| 119|0x0000000622c00000, 0x0000000622c00000, 0x0000000623000000|  0%| F|  |TAMS 0x0000000622c00000| PB 0x0000000622c00000| Untracked 
| 120|0x0000000623000000, 0x0000000623000000, 0x0000000623400000|  0%| F|  |TAMS 0x0000000623000000| PB 0x0000000623000000| Untracked 
| 121|0x0000000623400000, 0x0000000623400000, 0x0000000623800000|  0%| F|  |TAMS 0x0000000623400000| PB 0x0000000623400000| Untracked 
| 122|0x0000000623800000, 0x0000000623800000, 0x0000000623c00000|  0%| F|  |TAMS 0x0000000623800000| PB 0x0000000623800000| Untracked 
| 123|0x0000000623c00000, 0x0000000623c00000, 0x0000000624000000|  0%| F|  |TAMS 0x0000000623c00000| PB 0x0000000623c00000| Untracked 
| 124|0x0000000624000000, 0x0000000624000000, 0x0000000624400000|  0%| F|  |TAMS 0x0000000624000000| PB 0x0000000624000000| Untracked 
| 125|0x0000000624400000, 0x0000000624400000, 0x0000000624800000|  0%| F|  |TAMS 0x0000000624400000| PB 0x0000000624400000| Untracked 
| 126|0x0000000624800000, 0x0000000624b5c558, 0x0000000624c00000| 84%| E|  |TAMS 0x0000000624800000| PB 0x0000000624800000| Complete 

Card table byte_map: [0x00000246fb800000,0x00000246fc7e0000] _byte_map_base: 0x00000246f87d8000

Marking Bits: (CMBitMap*) 0x00000246e6dcb060
 Bits: [0x0000024680000000, 0x0000024687ec0000)

Polling page: 0x00000246e4b00000

Metaspace:

Usage:
  Non-class:      4.02 MB used.
      Class:    353.49 KB used.
       Both:      4.37 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       4.06 MB (  6%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     384.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       4.44 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  11.00 MB
       Class:  15.62 MB
        Both:  26.62 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 2.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 71.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 5.
num_chunk_merges: 0.
num_chunk_splits: 3.
num_chunks_enlarged: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=15Kb max_used=15Kb free=119152Kb
 bounds [0x00000246f2900000, 0x00000246f2b70000, 0x00000246f9d60000]
CodeHeap 'profiled nmethods': size=119104Kb used=105Kb max_used=105Kb free=118998Kb
 bounds [0x00000246ead60000, 0x00000246eafd0000, 0x00000246f21b0000]
CodeHeap 'non-nmethods': size=7488Kb used=1736Kb max_used=1736Kb free=5751Kb
 bounds [0x00000246f21b0000, 0x00000246f2420000, 0x00000246f2900000]
 total_blobs=389 nmethods=82 adapters=211
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.080 Thread 0x000002468817d370 nmethod 75 0x00000246ead77c10 code [0x00000246ead77da0, 0x00000246ead77f00]
Event: 0.080 Thread 0x000002468817d370   74       3       java.util.ImmutableCollections$Set12$1::next (95 bytes)
Event: 0.080 Thread 0x000002468817d370 nmethod 74 0x00000246ead77f90 code [0x00000246ead78160, 0x00000246ead783f0]
Event: 0.080 Thread 0x0000024688077b90 nmethod 67 0x00000246ead78510 code [0x00000246ead78700, 0x00000246ead78b70]
Event: 0.080 Thread 0x0000024688077b90   76       3       java.util.HashSet::add (20 bytes)
Event: 0.080 Thread 0x000002468817d370   78       3       java.util.AbstractMap::<init> (5 bytes)
Event: 0.080 Thread 0x0000024688077b90 nmethod 76 0x00000246ead78d10 code [0x00000246ead78ec0, 0x00000246ead79080]
Event: 0.080 Thread 0x0000024688077b90   64       1       java.lang.module.ResolvedModule::reference (5 bytes)
Event: 0.080 Thread 0x0000024688077b90 nmethod 64 0x00000246f2903610 code [0x00000246f29037a0, 0x00000246f2903870]
Event: 0.080 Thread 0x0000024688077b90   77       1       java.lang.module.ModuleDescriptor::isAutomatic (5 bytes)
Event: 0.081 Thread 0x000002468817d370 nmethod 78 0x00000246ead79190 code [0x00000246ead79340, 0x00000246ead794a8]
Event: 0.081 Thread 0x0000024688077b90 nmethod 77 0x00000246f2903910 code [0x00000246f2903aa0, 0x00000246f2903b70]
Event: 0.081 Thread 0x000002468817d370   79       1       java.lang.module.ModuleDescriptor$Exports::targets (5 bytes)
Event: 0.081 Thread 0x0000024688077b90   80       3       java.util.ImmutableCollections$Set12::iterator (9 bytes)
Event: 0.081 Thread 0x000002468817d370 nmethod 79 0x00000246f2903c10 code [0x00000246f2903da0, 0x00000246f2903e70]
Event: 0.081 Thread 0x000002468817d370   81       3       java.util.ImmutableCollections$Set12$1::<init> (32 bytes)
Event: 0.081 Thread 0x0000024688077b90 nmethod 80 0x00000246ead79590 code [0x00000246ead79740, 0x00000246ead79a30]
Event: 0.081 Thread 0x0000024688077b90   82       3       java.util.HashMap::<init> (11 bytes)
Event: 0.081 Thread 0x000002468817d370 nmethod 81 0x00000246ead79b10 code [0x00000246ead79cc0, 0x00000246ead79ef8]
Event: 0.081 Thread 0x0000024688077b90 nmethod 82 0x00000246ead7a010 code [0x00000246ead7a1c0, 0x00000246ead7a390]

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.007 Loaded shared library C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.dll
Event: 0.012 Loaded shared library C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\zip.dll

Deoptimization events (0 events):
No events

Classes loaded (20 events):
Event: 0.077 Loading class java/util/AbstractMap$1 done
Event: 0.077 Loading class java/util/AbstractMap$1$1
Event: 0.077 Loading class java/util/AbstractMap$1$1 done
Event: 0.078 Loading class jdk/internal/module/ModuleLoaderMap
Event: 0.078 Loading class jdk/internal/module/ModuleLoaderMap done
Event: 0.078 Loading class jdk/internal/module/ModuleLoaderMap$Mapper
Event: 0.078 Loading class jdk/internal/module/ModuleLoaderMap$Mapper done
Event: 0.078 Loading class jdk/internal/module/ModuleLoaderMap$Modules
Event: 0.078 Loading class jdk/internal/module/ModuleLoaderMap$Modules done
Event: 0.078 Loading class java/lang/ModuleLayer
Event: 0.078 Loading class java/lang/ModuleLayer done
Event: 0.078 Loading class java/util/ImmutableCollections$ListItr
Event: 0.078 Loading class java/util/ListIterator
Event: 0.078 Loading class java/util/ListIterator done
Event: 0.078 Loading class java/util/ImmutableCollections$ListItr done
Event: 0.081 Loading class jdk/internal/loader/AbstractClassLoaderValue$Memoizer
Event: 0.081 Loading class jdk/internal/loader/AbstractClassLoaderValue$Memoizer done
Event: 0.081 Loading class jdk/internal/module/ServicesCatalog$ServiceProvider
Event: 0.081 Loading class jdk/internal/module/ServicesCatalog$ServiceProvider done
Event: 0.081 Loading class java/util/concurrent/CopyOnWriteArrayList

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (11 events):
Event: 0.011 Thread 0x00000246e6d66ee0 Thread added: 0x00000246e6d66ee0
Event: 0.053 Thread 0x00000246e6d66ee0 Thread added: 0x0000024687ff4b70
Event: 0.059 Thread 0x00000246e6d66ee0 Thread added: 0x000002468801b790
Event: 0.059 Thread 0x00000246e6d66ee0 Thread added: 0x00000246880644c0
Event: 0.060 Thread 0x00000246e6d66ee0 Thread added: 0x0000024688081c90
Event: 0.060 Thread 0x00000246e6d66ee0 Thread added: 0x00000246880826f0
Event: 0.060 Thread 0x00000246e6d66ee0 Thread added: 0x0000024688083150
Event: 0.061 Thread 0x00000246e6d66ee0 Thread added: 0x0000024688075d30
Event: 0.061 Thread 0x00000246e6d66ee0 Thread added: 0x0000024688076ae0
Event: 0.061 Thread 0x00000246e6d66ee0 Thread added: 0x0000024688077b90
Event: 0.078 Thread 0x0000024688077b90 Thread added: 0x000002468817d370


Dynamic libraries:
0x00007ff658fc0000 - 0x00007ff658fca000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.exe
0x00007ff9ad010000 - 0x00007ff9ad227000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff9ab770000 - 0x00007ff9ab834000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff9aa320000 - 0x00007ff9aa6f0000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff9aaad0000 - 0x00007ff9aabe1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff973420000 - 0x00007ff973438000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\jli.dll
0x00007ff973c80000 - 0x00007ff973c9b000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\VCRUNTIME140.dll
0x00007ff9ace10000 - 0x00007ff9acfc1000 	C:\WINDOWS\System32\USER32.dll
0x00007ff9aac70000 - 0x00007ff9aac96000 	C:\WINDOWS\System32\win32u.dll
0x00007ff9ab910000 - 0x00007ff9ab939000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff97d080000 - 0x00007ff97d31c000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe\COMCTL32.dll
0x00007ff9aa6f0000 - 0x00007ff9aa813000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff9abc40000 - 0x00007ff9abce7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff9aa110000 - 0x00007ff9aa1aa000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff9abd00000 - 0x00007ff9abd31000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff973ca0000 - 0x00007ff973cff000 	C:\Program Files\MacType\MacType64.dll
0x00007ff9abb80000 - 0x00007ff9abc31000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff9aaff0000 - 0x00007ff9ab098000 	C:\WINDOWS\System32\sechost.dll
0x00007ff9aaaa0000 - 0x00007ff9aaac8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff9aae60000 - 0x00007ff9aaf78000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff973af0000 - 0x00007ff973c73000 	C:\Program Files\MacType\MacType64.Core.dll
0x00007ff983d20000 - 0x00007ff983d2c000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\vcruntime140_1.dll
0x00007ff8af9a0000 - 0x00007ff8afa2d000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\msvcp140.dll
0x00007ff8c1ca0000 - 0x00007ff8c2a64000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\server\jvm.dll
0x00007ff9ab4f0000 - 0x00007ff9ab561000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff9a9fa0000 - 0x00007ff9a9fed000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff9a1900000 - 0x00007ff9a1934000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff99da00000 - 0x00007ff99da0a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff9a9f80000 - 0x00007ff9a9f93000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff9a9060000 - 0x00007ff9a9078000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff983d10000 - 0x00007ff983d1a000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\jimage.dll
0x00007ff9a7410000 - 0x00007ff9a7643000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff9abd40000 - 0x00007ff9ac0d1000 	C:\WINDOWS\System32\combase.dll
0x00007ff9ab300000 - 0x00007ff9ab3d8000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff98abd0000 - 0x00007ff98ac02000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00000246e6350000 - 0x00000246e63cb000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff973440000 - 0x00007ff973460000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.dll
0x00007ff973010000 - 0x00007ff973028000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\zip.dll
0x00007ff9ac560000 - 0x00007ff9ace01000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff9aa8a0000 - 0x00007ff9aa9df000 	C:\WINDOWS\System32\wintypes.dll
0x00007ff9a7f50000 - 0x00007ff9a886a000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff9ab3e0000 - 0x00007ff9ab4ea000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff9abaf0000 - 0x00007ff9abb59000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff9aa000000 - 0x00007ff9aa025000 	C:\WINDOWS\SYSTEM32\profapi.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5840_none_2710ea077384a4fe;C:\Program Files\MacType;C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'http://*************': 
java_class_path (initial): C:/Users/<USER>/AppData/Local/Programs/IntelliJ IDEA Ultimate/plugins/vcs-git/lib/git4idea-rt.jar;C:/Users/<USER>/AppData/Local/Programs/IntelliJ IDEA Ultimate/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 4                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 15                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8506048512                                {product} {ergonomic}
   size_t MaxNewSize                               = 5100273664                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8506048512                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Users\<USER>\Develop\env\jdk\graalvm-jdk-21.0.3+7.1
CLASSPATH=C:\Users\<USER>\Develop\env\jdk\graalvm-jdk-21.0.3+7.1\lib
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0;C:\windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS;C:\WINDOWS\system32;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\OpenSSH;C:\Users\<USER>\Develop\env\jdk\graalvm-jdk-21.0.3+7.1\bin;C:\Users\<USER>\Develop\env\jdk\graalvm-jdk-21.0.3+7.1\jre\bin;C:\Users\<USER>\Develop\env\maven\apache-maven-3.8.6\bin;C:\Program Files\Bandizip;C:\Program Files\dotnet;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\HP\HP One Agent;C:\Program Files (x86)\NetSarang\Xshell 8;C:\Users\<USER>\Develop\env\gradle\gradle-8.14\bin;C:\Program Files\Git\cmd;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files\vfox;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\PowerShell\7;C:\Users\<USER>\.version-fox\cache\python\current\Scripts;C:\Users\<USER>\.version-fox\cache\python\current;C:\Users\<USER>\.version-fox\cache\nodejs\current;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\go\bin;C:\Program Files\nodejs;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Local\XPipe\cli\bin;C:\Users\<USER>\go\bin;C:\nvm4w\nodejs;C:\Users\<USER>\.deno\bin;C:\Users\<USER>\.jetbrains\helm;C:\Users\<USER>\AppData\Local\Programs\EmEditor
USERNAME=meilon
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 154 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 14, weak refs: 0

JNI global refs memory usage: 835, weak refs: 201

Process memory usage:
Resident Set Size: 35988K (0% of 33214232K total physical memory with 4400460K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 4471K

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 0 days 7:52 hours
Hyper-V role detected

CPU: total 20 (initial active 20) (10 cores per cpu, 2 threads per core) family 6 model 154 stepping 3 microcode 0x437, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv, serialize, rdtscp, rdpid, fsrm, f16c, cet_ibt, cet_ss
Processor Information for processor 0
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 1
  Max Mhz: 2500, Current Mhz: 1532, Mhz Limit: 2500
Processor Information for processor 2
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 3
  Max Mhz: 2500, Current Mhz: 1532, Mhz Limit: 2500
Processor Information for processor 4
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 5
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 6
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 7
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 8
  Max Mhz: 2500, Current Mhz: 1532, Mhz Limit: 2500
Processor Information for processor 9
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 10
  Max Mhz: 2500, Current Mhz: 1532, Mhz Limit: 2500
Processor Information for processor 11
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 12
  Max Mhz: 2500, Current Mhz: 1527, Mhz Limit: 2500
Processor Information for processor 13
  Max Mhz: 2500, Current Mhz: 1527, Mhz Limit: 2500
Processor Information for processor 14
  Max Mhz: 2500, Current Mhz: 1527, Mhz Limit: 2500
Processor Information for processor 15
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 16
  Max Mhz: 2500, Current Mhz: 1527, Mhz Limit: 2500
Processor Information for processor 17
  Max Mhz: 2500, Current Mhz: 1527, Mhz Limit: 2500
Processor Information for processor 18
  Max Mhz: 2500, Current Mhz: 2500, Mhz Limit: 2500
Processor Information for processor 19
  Max Mhz: 2500, Current Mhz: 1527, Mhz Limit: 2500

Memory: 4k page, system-wide physical 32435M (4297M free)
TotalPageFile size 32435M (AvailPageFile size 4M)
current process WorkingSet (physical memory assigned to process): 35M, peak: 35M
current process commit charge ("private bytes"): 606M, peak: 607M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-b1038.58) for windows-amd64 JRE (21.0.7+6-b1038.58), built on 2025-07-07 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
